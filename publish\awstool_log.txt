2025-08-03 07:26:05 [信息] AWS自动注册工具启动
2025-08-03 07:26:05 [信息] 程序版本: 1.0.0.0
2025-08-03 07:26:05 [信息] 启动时间: 2025-08-03 07:26:05
2025-08-03 07:26:05 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 07:26:05 [信息] 线程数量已选择: 1
2025-08-03 07:26:05 [信息] 线程数量选择初始化完成
2025-08-03 07:26:05 [信息] 程序初始化完成
2025-08-03 07:26:29 [信息] 线程数量已选择: 3
2025-08-03 07:26:30 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 07:26:34 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-以色列.txt
2025-08-03 07:26:42 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-以色列.txt
2025-08-03 07:26:42 [信息] 成功加载 3 条数据
2025-08-03 07:26:48 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 07:26:48 [信息] 开始启动多线程注册，线程数量: 3
2025-08-03 07:26:48 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 3
2025-08-03 07:26:48 [信息] 所有线程已停止并清理
2025-08-03 07:26:48 [信息] 正在初始化多线程服务...
2025-08-03 07:26:48 [信息] 千川手机API服务已初始化
2025-08-03 07:26:48 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-03 07:26:48 [信息] 多线程服务初始化完成
2025-08-03 07:26:48 [信息] 数据分配完成：共3条数据分配给3个线程
2025-08-03 07:26:48 [信息] 线程1分配到1条数据
2025-08-03 07:26:48 [信息] 线程2分配到1条数据
2025-08-03 07:26:48 [信息] 线程3分配到1条数据
2025-08-03 07:26:48 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:26:48 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:26:48 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 07:26:48 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:48 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=10 GB
2025-08-03 07:26:48 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 07:26:48 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:26:48 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 07:26:48 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:26:48 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:26:48 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 07:26:48 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:48 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=8 GB
2025-08-03 07:26:48 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-03 07:26:48 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:26:48 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 07:26:48 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:26:48 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:26:48 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=IL
2025-08-03 07:26:48 [信息] 为国家代码 IL 生成智能指纹: 时区=Asia/Jerusalem, 语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:48 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=24 GB
2025-08-03 07:26:48 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-03 07:26:48 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:26:48 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=IL, 时区=Asia/Jerusalem
2025-08-03 07:26:48 [信息] 多线程注册启动成功，共3个线程
2025-08-03 07:26:48 线程2：[信息] 开始启动注册流程
2025-08-03 07:26:48 线程3：[信息] 开始启动注册流程
2025-08-03 07:26:48 线程1：[信息] 开始启动注册流程
2025-08-03 07:26:48 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-03 07:26:48 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 07:26:48 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:26:48 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:26:48 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-03 07:26:48 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:26:48 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:26:48 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:26:48 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:26:48 [信息] 多线程管理窗口已初始化
2025-08-03 07:26:49 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:26:49 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 07:26:49 [信息] 多线程管理窗口已打开
2025-08-03 07:26:49 [信息] 多线程注册启动成功，共3个线程
2025-08-03 07:26:54 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:26:54 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 07:26:54 线程3：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 07:26:54 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 07:26:54 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 07:26:54 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:26:55 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:26:55 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 07:26:55 线程1：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 07:26:55 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 07:26:55 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 07:26:55 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:26:55 [信息] UniformGrid列数已更新为: 2
2025-08-03 07:26:55 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-03 07:26:55 线程2：[信息] [信息] 多线程模式根据指纹国家代码 IL 设置浏览器语言: עברית (进度: 0%)
2025-08-03 07:26:55 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=IL, 语言=עברית, 参数=--lang=he-IL
2025-08-03 07:26:55 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-03 07:26:55 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:26:56 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:26:56 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:26:56 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:26:58 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 07:26:58 线程2：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:58 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 31.7683, 35.2137 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=31.7683, 经度=35.2137
2025-08-03 07:26:58 线程3：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 07:26:58 线程3：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:58 线程3：[信息] [信息] 多线程模式使用指纹地理位置: 31.7683, 35.2137 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=31.7683, 经度=35.2137
2025-08-03 07:26:58 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 18核 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=8 GB
2025-08-03 07:26:58 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 4核 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=24 GB
2025-08-03 07:26:58 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Jerusalem (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Jerusalem
2025-08-03 07:26:58 线程1：[信息] [信息] 多线程模式使用指纹语言: he-IL,he;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器语言设置: 多线程模式使用指纹语言=he-IL,he;q=0.9,en;q=0.8
2025-08-03 07:26:58 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 32.7057, 35.2044 (进度: 0%)
2025-08-03 07:26:58 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=32.7057, 经度=35.2044
2025-08-03 07:26:59 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 14核 (进度: 0%)
2025-08-03 07:26:59 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=14核, RAM=10 GB
2025-08-03 07:27:01 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:27:01 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:27:01 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:27:02 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 14
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-U6V7W8X
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1879x1135
   • 可用区域: 1879x1095

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.66
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:27:02 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 14    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-U6V7W8X    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1879x1135    • 可用区域: 1879x1095   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.66    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:27:02 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 07:27:02 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:27:02 线程1：[信息] 浏览器启动成功
2025-08-03 07:27:02 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:27:02 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:27:02 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:27:02 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:27:02 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:27:02 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:27:02 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 24 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5E6F7A8B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1812x1051
   • 可用区域: 1812x1011

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: unknown
   • 电池API支持: True
   • 电池电量: 0.62
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:27:03 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 24 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5E6F7A8B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1812x1051    • 可用区域: 1812x1011   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: unknown    • 电池API支持: True    • 电池电量: 0.62    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:27:03 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_007
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 1786x1087
   • 可用区域: 1786x1047

🌍 地区语言信息:
   • 主语言: he-IL
   • 语言列表: he-IL,en-US
   • 时区偏移: -120分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.81
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:27:03 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_007    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 1786x1087    • 可用区域: 1786x1047   地区语言信息:    • 主语言: he-IL    • 语言列表: he-IL,en-US    • 时区偏移: -120分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.81    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:27:03 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 07:27:03 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:27:03 线程2：[信息] 浏览器启动成功
2025-08-03 07:27:03 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:27:03 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:27:03 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-03 07:27:03 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:27:03 线程3：[信息] 浏览器启动成功
2025-08-03 07:27:03 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:27:03 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:27:03 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:27:03 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 07:27:03 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 07:27:03 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:27:04 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:27:04 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 07:27:04 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 07:27:04 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:27:19 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 07:27:19 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 07:27:19 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 07:27:19 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 07:27:19 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 07:27:19 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 07:27:19 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 07:27:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 07:27:19 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 07:27:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 07:27:20 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 07:27:20 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 07:27:23 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 07:27:23 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 07:27:23 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 07:27:23 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 07:27:23 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 07:27:23 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 07:27:23 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 07:27:23 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 07:27:23 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:27:23 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 07:27:24 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:27:24 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 07:27:25 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 07:27:26 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 07:27:26 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 07:27:26 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 07:27:26 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 07:27:26 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:27:26 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 07:27:26 [信息] [线程1] 已删除旧的响应文件
2025-08-03 07:27:26 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 07:27:26 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 07:27:26 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 07:27:26 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 07:27:26 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 07:27:26 [信息] [线程2] 已删除旧的响应文件
2025-08-03 07:27:26 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-03 07:27:26 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 07:27:26 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:27:28 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 07:27:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:27:28
2025-08-03 07:27:28 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 07:27:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:27:28
2025-08-03 07:27:31 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 07:27:31 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:27:31
2025-08-03 07:27:31 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 07:27:31 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:27:31
2025-08-03 07:27:33 [信息] [线程1] 邮箱验证码获取成功: 808862，立即停止重复请求
2025-08-03 07:27:33 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 07:27:33 [信息] [线程1] 已清理响应文件
2025-08-03 07:27:33 线程1：[信息] [信息] 验证码获取成功: 808862，正在自动填入... (进度: 25%)
2025-08-03 07:27:33 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 07:27:33 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 07:27:34 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 07:27:34 [信息] 线程1完成第二页事件已处理
2025-08-03 07:27:34 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-03 07:27:34 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 07:27:34 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-03 07:27:34 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-03 07:27:34 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-03 07:27:34 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-03 07:27:34 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-03 07:27:34 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-03 07:27:34 [信息] 批量获取3个手机号码成功
2025-08-03 07:27:34 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 07:27:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:27:34
2025-08-03 07:27:34 [信息] [线程2] 邮箱验证码获取成功: 204857，立即停止重复请求
2025-08-03 07:27:34 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-03 07:27:34 [信息] [线程2] 已清理响应文件
2025-08-03 07:27:34 线程2：[信息] [信息] 验证码获取成功: 204857，正在自动填入... (进度: 25%)
2025-08-03 07:27:34 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 07:27:36 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 07:27:36 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 07:27:36 [信息] 线程2完成第二页事件已处理
2025-08-03 07:27:36 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-03 07:27:36 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 07:27:36 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-03 07:27:36 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-03 07:27:36 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-03 07:27:36 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-03 07:27:37 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-03 07:27:37 线程1：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-03 07:27:37 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-03 07:27:37 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-03 07:27:37 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-03 07:27:38 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-03 07:27:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-03 07:27:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-03 07:27:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-03 07:27:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-03 07:27:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-03 07:27:41 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-03 07:27:41 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870307116","phoneId":"8446007116993","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5806978,"phoneNo":"4367870307116","projectId":804413,"startTime":"2025-08-03 07:27:40","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"8fbe71e8-6f3f-4c29-b5ab-6a1022ae3f5f"}
2025-08-03 07:27:42 [信息] [千川API] 获取手机号码成功: +4367870307116
2025-08-03 07:27:42 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870319456","phoneId":"8446019456539","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5806977,"phoneNo":"4367870319456","projectId":804413,"startTime":"2025-08-03 07:27:40","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"122293c9-9960-49ae-b182-62e402c55441"}
2025-08-03 07:27:42 [信息] [千川API] 获取手机号码成功: +4367870319456
2025-08-03 07:27:45 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-03 07:27:45 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-03 07:27:45 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-03 07:27:47 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-03 07:27:47 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-03 07:27:47 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-03 07:27:52 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-03 07:27:52 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-03 07:27:52 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-03 07:27:52 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-03 07:27:58 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-03 07:27:59 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-03 07:27:59 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-03 07:27:59 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-03 07:27:59 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-03 07:27:59 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-03 07:27:59 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-03 07:27:59 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-03 07:27:59 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-03 07:27:59 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-03 07:27:59 线程1：[信息] [信息] 数据国家代码为IL，需要选择Israel (进度: 48%)
2025-08-03 07:27:59 线程2：[信息] [信息] 数据国家代码为IL，需要选择Israel (进度: 48%)
2025-08-03 07:27:59 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-03 07:27:59 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-03 07:28:01 线程1：[信息] [信息] 已选择国家: Israel (进度: 48%)
2025-08-03 07:28:01 线程1：[信息] [信息] 已成功选择国家: Israel (进度: 48%)
2025-08-03 07:28:01 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-03 07:28:01 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-03 07:28:01 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-03 07:28:02 线程2：[信息] [信息] 已选择国家: Israel (进度: 48%)
2025-08-03 07:28:02 线程2：[信息] [信息] 已成功选择国家: Israel (进度: 48%)
2025-08-03 07:28:02 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-03 07:28:02 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-03 07:28:02 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-03 07:28:03 线程1：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-03 07:28:03 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-03 07:28:03 线程1：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-03 07:28:03 线程1：[信息] [信息] 千川手机号码获取成功: +4367870307116 (进度: 48%)
2025-08-03 07:28:04 线程1：[信息] [信息] 千川手机号码已自动填入: +4367870307116 (进度: 48%)
2025-08-03 07:28:04 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-03 07:28:04 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-03 07:28:04 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-03 07:28:04 线程2：[信息] [信息] 千川手机号码获取成功: +4367870319456 (进度: 48%)
2025-08-03 07:28:04 线程2：[信息] [信息] 千川手机号码已自动填入: +4367870319456 (进度: 48%)
2025-08-03 07:28:05 线程1：[信息] [信息] 使用已保存的手机号码: 67870307116 (进度: 48%)
2025-08-03 07:28:05 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-03 07:28:05 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870311818","phoneId":"************","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5806979,"phoneNo":"4367870311818","projectId":804413,"startTime":"2025-08-03 07:28:04","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"afd6069a-ab9b-45d0-aab0-4de063b8dc18"}
2025-08-03 07:28:05 [信息] [千川API] 获取手机号码成功: +4367870311818
2025-08-03 07:28:05 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870311931","phoneId":"8448311931277","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5806980,"phoneNo":"4367870311931","projectId":804413,"startTime":"2025-08-03 07:28:04","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"3a61040d-c854-4a75-b3d0-b1dbe8ffc9b7"}
2025-08-03 07:28:05 [信息] [千川API] 获取手机号码成功: +4367870311931
2025-08-03 07:28:05 线程2：[信息] [信息] 使用已保存的手机号码: 67870319456 (进度: 48%)
2025-08-03 07:28:05 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-03 07:28:08 线程1：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-03 07:28:08 线程2：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-03 07:28:09 线程1：[信息] [信息] 正在选择月份: May (进度: 48%)
2025-08-03 07:28:09 线程1：[信息] [信息] 已选择月份（标准选项）: May (进度: 48%)
2025-08-03 07:28:09 线程2：[信息] [信息] 正在选择月份: April (进度: 48%)
2025-08-03 07:28:09 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 48%)
2025-08-03 07:28:09 线程1：[信息] [信息] 正在选择年份: 2026 (进度: 48%)
2025-08-03 07:28:12 线程1：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 48%)
2025-08-03 07:28:12 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-03 07:28:12 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-03 07:28:12 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-03 07:28:12 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 正在选择年份: 2028 (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-03 07:28:13 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-03 07:28:17 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-03 07:28:17 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-03 07:28:18 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-03 07:28:18 线程1：[信息] [信息] 已清空并重新填写手机号码: 67870307116 (进度: 48%)
2025-08-03 07:28:18 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-03 07:28:18 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-03 07:28:18 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870319456 (进度: 48%)
2025-08-03 07:28:19 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-03 07:28:20 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-03 07:28:20 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:28:20 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-03 07:28:20 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:28:21 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-03 07:28:21 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:28:21 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-03 07:28:21 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:28:23 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:28:23 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:28:24 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:28:24 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:28:26 线程3：[信息] [信息] 所有自动线程已停止 (进度: 98%)
2025-08-03 07:28:26 线程3：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 98%)
2025-08-03 07:28:26 线程3：[信息] 已暂停
2025-08-03 07:28:26 [信息] 线程3已暂停
2025-08-03 07:28:26 [信息] 线程3已暂停
2025-08-03 07:28:26 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34647 字节 (进度: 100%)
2025-08-03 07:28:26 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34647字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:28:26 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:28:27 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"c6bb5g"},"taskId":"63c72c4e-6ff8-11f0-8c61-861c57ef4db4"} (进度: 100%)
2025-08-03 07:28:27 线程1：[信息] [信息] 第六页第1次识别结果: c6bb5g → 转换为小写: c6bb5g (进度: 100%)
2025-08-03 07:28:27 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:28:27 线程1：[信息] [信息] 第六页已填入验证码: c6bb5g (进度: 100%)
2025-08-03 07:28:27 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34647 字节 (进度: 100%)
2025-08-03 07:28:27 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34647字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:28:27 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:28:27 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:28:28 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g8m7pt"},"taskId":"644f43e0-6ff8-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 07:28:28 线程2：[信息] [信息] 第六页第1次识别结果: g8m7pt → 转换为小写: g8m7pt (进度: 100%)
2025-08-03 07:28:28 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:28:28 线程2：[信息] [信息] 第六页已填入验证码: g8m7pt (进度: 100%)
2025-08-03 07:28:28 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:28:31 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:28:31 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:28:32 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 07:28:32 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-03 07:28:34 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:28:35 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:28:37 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31293 字节 (进度: 100%)
2025-08-03 07:28:37 线程1：[信息] [信息] ✅ 图片验证通过：200x70px，31293字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:28:37 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:28:38 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:28:38 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:28:38 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:28:38 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:28:38 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"8bmxzn"},"taskId":"6a789d5c-6ff8-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 07:28:38 线程1：[信息] [信息] 第六页第2次识别结果: 8bmxzn → 转换为小写: 8bmxzn (进度: 100%)
2025-08-03 07:28:38 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:28:38 线程1：[信息] [信息] 第六页已填入验证码: 8bmxzn (进度: 100%)
2025-08-03 07:28:38 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:28:42 线程1：[信息] [信息] 第2次图形验证码识别成功 (进度: 100%)
2025-08-03 07:28:42 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:28:43 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-03 07:28:43 线程2：[信息] [信息] 线程2等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-03 07:28:45 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:28:48 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:28:48 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:28:48 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:28:48 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:28:48 线程2：[信息] [信息] 线程2第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-03 07:28:48 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:28:48 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:28:49 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"af7b835a-fa6f-479d-ac2f-350b7d906a5d"}
2025-08-03 07:28:49 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:28:49 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:28:49 线程2：[信息] [信息] 线程2第1次获取千川验证码失败，3秒后重试...（剩余9次尝试） (进度: 100%)
2025-08-03 07:28:52 线程2：[信息] [信息] 线程2第2次尝试获取千川验证码...（剩余8次尝试） (进度: 100%)
2025-08-03 07:28:52 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:28:52 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:28:52 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"5de3fe25-097e-4da9-9dfb-bc830dfa0b0d"}
2025-08-03 07:28:52 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:28:52 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:28:52 线程2：[信息] [信息] 线程2第2次获取千川验证码失败，3秒后重试...（剩余8次尝试） (进度: 100%)
2025-08-03 07:28:53 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 07:28:53 线程1：[信息] [信息] 线程1等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-03 07:28:55 线程2：[信息] [信息] 线程2第3次尝试获取千川验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:28:55 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:28:55 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:28:55 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"0ef5c965-4ecd-4ff6-898b-4b91dd60577a"}
2025-08-03 07:28:55 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:28:55 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:28:55 线程2：[信息] [信息] 线程2第3次获取千川验证码失败，3秒后重试...（剩余7次尝试） (进度: 100%)
2025-08-03 07:28:58 线程1：[信息] [信息] 线程1第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-03 07:28:58 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:28:58 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:28:58 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"44704da4-b94f-47d9-ac88-f4bde93ef0f4"}
2025-08-03 07:28:58 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:28:58 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:28:58 线程1：[信息] [信息] 线程1第1次获取千川验证码失败，3秒后重试...（剩余9次尝试） (进度: 100%)
2025-08-03 07:28:58 线程2：[信息] [信息] 线程2第4次尝试获取千川验证码...（剩余6次尝试） (进度: 100%)
2025-08-03 07:28:58 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:28:58 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:28:58 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"4c92dfb9-3487-4f87-a079-c3727ff1e67c"}
2025-08-03 07:28:58 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:28:58 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:28:58 线程2：[信息] [信息] 线程2第4次获取千川验证码失败，3秒后重试...（剩余6次尝试） (进度: 100%)
2025-08-03 07:29:01 线程1：[信息] [信息] 线程1第2次尝试获取千川验证码...（剩余8次尝试） (进度: 100%)
2025-08-03 07:29:01 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:01 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:01 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"48516655-24cf-4582-984b-d931598ae28a"}
2025-08-03 07:29:01 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:01 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:01 线程1：[信息] [信息] 线程1第2次获取千川验证码失败，3秒后重试...（剩余8次尝试） (进度: 100%)
2025-08-03 07:29:02 线程2：[信息] [信息] 线程2第5次尝试获取千川验证码...（剩余5次尝试） (进度: 100%)
2025-08-03 07:29:02 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:02 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:02 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"ec6a2e61-842a-4700-adee-aa60698c174d"}
2025-08-03 07:29:02 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:02 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:02 线程2：[信息] [信息] 线程2第5次获取千川验证码失败，3秒后重试...（剩余5次尝试） (进度: 100%)
2025-08-03 07:29:04 线程1：[信息] [信息] 线程1第3次尝试获取千川验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:29:04 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:04 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:04 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"df136013-0747-425a-b473-e6d7b69d23cf"}
2025-08-03 07:29:04 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:04 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:04 线程1：[信息] [信息] 线程1第3次获取千川验证码失败，3秒后重试...（剩余7次尝试） (进度: 100%)
2025-08-03 07:29:05 线程2：[信息] [信息] 线程2第6次尝试获取千川验证码...（剩余4次尝试） (进度: 100%)
2025-08-03 07:29:05 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:05 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:05 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"896cba3c-2b96-47f0-a35a-c76e732b3bc2"}
2025-08-03 07:29:05 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:05 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:05 线程2：[信息] [信息] 线程2第6次获取千川验证码失败，3秒后重试...（剩余4次尝试） (进度: 100%)
2025-08-03 07:29:07 线程1：[信息] [信息] 线程1第4次尝试获取千川验证码...（剩余6次尝试） (进度: 100%)
2025-08-03 07:29:07 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:07 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:08 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"f352e57e-8267-4d8b-9ab4-f335a0b0d7c8"}
2025-08-03 07:29:08 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:08 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:08 线程1：[信息] [信息] 线程1第4次获取千川验证码失败，3秒后重试...（剩余6次尝试） (进度: 100%)
2025-08-03 07:29:08 线程2：[信息] [信息] 线程2第7次尝试获取千川验证码...（剩余3次尝试） (进度: 100%)
2025-08-03 07:29:08 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:08 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:08 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"8960b646-f4b2-4800-8f1e-29fa2ca4c579"}
2025-08-03 07:29:08 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:08 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:08 线程2：[信息] [信息] 线程2第7次获取千川验证码失败，3秒后重试...（剩余3次尝试） (进度: 100%)
2025-08-03 07:29:11 线程1：[信息] [信息] 线程1第5次尝试获取千川验证码...（剩余5次尝试） (进度: 100%)
2025-08-03 07:29:11 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:11 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:11 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"03bd6e5f-8ff5-464f-a04f-0ebc413c6ddb"}
2025-08-03 07:29:11 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:11 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:11 线程1：[信息] [信息] 线程1第5次获取千川验证码失败，3秒后重试...（剩余5次尝试） (进度: 100%)
2025-08-03 07:29:11 线程2：[信息] [信息] 线程2第8次尝试获取千川验证码...（剩余2次尝试） (进度: 100%)
2025-08-03 07:29:11 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:11 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:12 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"c8b3e9aa-1535-406a-b71b-571329ee26e6"}
2025-08-03 07:29:12 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:12 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:12 线程2：[信息] [信息] 线程2第8次获取千川验证码失败，3秒后重试...（剩余2次尝试） (进度: 100%)
2025-08-03 07:29:14 线程1：[信息] [信息] 线程1第6次尝试获取千川验证码...（剩余4次尝试） (进度: 100%)
2025-08-03 07:29:14 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:14 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:14 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"f10073b9-ae06-475e-8612-d7442288e822"}
2025-08-03 07:29:14 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:14 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:14 线程1：[信息] [信息] 线程1第6次获取千川验证码失败，3秒后重试...（剩余4次尝试） (进度: 100%)
2025-08-03 07:29:15 线程2：[信息] [信息] 线程2第9次尝试获取千川验证码...（剩余1次尝试） (进度: 100%)
2025-08-03 07:29:15 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:15 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:15 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"fb428c40-a693-4443-af9d-bb0a308e6226"}
2025-08-03 07:29:15 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:15 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:15 线程2：[信息] [信息] 线程2第9次获取千川验证码失败，3秒后重试...（剩余1次尝试） (进度: 100%)
2025-08-03 07:29:17 线程1：[信息] [信息] 线程1第7次尝试获取千川验证码...（剩余3次尝试） (进度: 100%)
2025-08-03 07:29:17 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:17 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:17 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"4c740cfe-29af-4594-95ed-4d42174ae4bd"}
2025-08-03 07:29:18 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:18 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:18 线程1：[信息] [信息] 线程1第7次获取千川验证码失败，3秒后重试...（剩余3次尝试） (进度: 100%)
2025-08-03 07:29:18 线程2：[信息] [信息] 线程2第10次尝试获取千川验证码...（剩余0次尝试） (进度: 100%)
2025-08-03 07:29:18 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:18 [信息] [千川API] 获取验证码请求: 4367870311931
2025-08-03 07:29:18 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"aee08e7c-0b81-4d7c-934e-8843f41895ad"}
2025-08-03 07:29:18 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:18 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:18 线程2：[信息] [信息] 线程2第10次获取千川验证码失败，3秒后重试...（剩余0次尝试） (进度: 100%)
2025-08-03 07:29:21 线程1：[信息] [信息] 线程1第8次尝试获取千川验证码...（剩余2次尝试） (进度: 100%)
2025-08-03 07:29:21 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:21 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:21 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"3b74811f-616b-4c5f-8435-a1adcd20c86f"}
2025-08-03 07:29:21 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:21 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:21 线程1：[信息] [信息] 线程1第8次获取千川验证码失败，3秒后重试...（剩余2次尝试） (进度: 100%)
2025-08-03 07:29:22 线程2：[信息] [信息] 线程2千川验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-03 07:29:22 [信息] 线程2千川手机号码已加入黑名单队列: +4367870311931 (原因: 验证码获取超时失败)
2025-08-03 07:29:22 线程2：[信息] [信息] 线程2验证码获取失败: 验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-03 07:29:22 线程2：[信息] [信息] 自动获取验证码异常: Object reference not set to an instance of an object. (进度: 100%)
2025-08-03 07:29:22 线程2：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-03 07:29:22 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-03 07:29:24 线程1：[信息] [信息] 线程1第9次尝试获取千川验证码...（剩余1次尝试） (进度: 100%)
2025-08-03 07:29:24 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:24 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:24 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"579837ca-92e7-412e-81d2-4979c6a7c7b0"}
2025-08-03 07:29:24 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:24 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:24 线程1：[信息] [信息] 线程1第9次获取千川验证码失败，3秒后重试...（剩余1次尝试） (进度: 100%)
2025-08-03 07:29:27 线程1：[信息] [信息] 线程1第10次尝试获取千川验证码...（剩余0次尝试） (进度: 100%)
2025-08-03 07:29:27 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-03 07:29:27 [信息] [千川API] 获取验证码请求: 4367870311818
2025-08-03 07:29:27 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"0934e2ca-d4dd-4c47-9f37-9b61675c80ab"}
2025-08-03 07:29:27 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-03 07:29:27 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 07:29:27 线程1：[信息] [信息] 线程1第10次获取千川验证码失败，3秒后重试...（剩余0次尝试） (进度: 100%)
2025-08-03 07:29:31 线程1：[信息] [信息] 线程1千川验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-03 07:29:31 [信息] 线程1千川手机号码已加入黑名单队列: +4367870311818 (原因: 验证码获取超时失败)
2025-08-03 07:29:31 线程1：[信息] [信息] 线程1验证码获取失败: 验证码获取超时失败（已尝试10次） (进度: 100%)
2025-08-03 07:29:31 线程1：[信息] [信息] 自动获取验证码异常: Object reference not set to an instance of an object. (进度: 100%)
2025-08-03 07:29:31 线程1：[信息] [信息] 千川API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-08-03 07:29:31 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-03 07:29:48 [信息] 定时检查发现2个待拉黑千川手机号码，开始批量拉黑
2025-08-03 07:29:48 [信息] 开始拉黑2个千川手机号码
2025-08-03 07:29:48 [信息] [千川API] 拉黑手机号码: 4367870311931
2025-08-03 07:29:49 [信息] [千川API] 手机号码拉黑成功: 4367870311931
2025-08-03 07:29:49 [信息] [千川API] 拉黑手机号码: 4367870311818
2025-08-03 07:29:49 [信息] [千川API] 手机号码拉黑成功: 4367870311818
2025-08-03 07:29:50 [信息] [千川API] 千川批量拉黑完成: 成功2个, 失败0个
2025-08-03 07:29:50 [信息] 定时批量拉黑完成: 千川批量拉黑完成: 成功2个, 失败0个
2025-08-03 07:32:37 线程3：[信息] [信息] 注册失败: Target page, context or browser has been closed (进度: 98%)
2025-08-03 07:32:37 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-03 07:32:37 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:32:37 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:32:37 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:32:37 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:32:37 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:32:37 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-03 07:32:37 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:33:21 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-03 07:33:21 线程1：[信息] [信息] 🔧 [DEBUG] 未检测到第七页特征 (进度: 100%)
2025-08-03 07:33:21 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:33:21 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:33:21 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:33:26 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 07:33:26 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 07:33:26 线程1：[信息] 已暂停
2025-08-03 07:33:26 [信息] 线程1已暂停
2025-08-03 07:33:26 [信息] 线程1已暂停
2025-08-03 07:33:27 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-03 07:33:27 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:33:27 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:33:27 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🎯 找到匹配链接: 'Go to the AWS Management Console' → 第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] ✅ 直接确认为第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息]  智能检测到当前在第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 智能检测到当前在第9页，开始智能处理... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🔑 第9页：点击控制台链接并开始AWS访问密钥提取流程... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🎯 找到匹配链接: 'Go to the AWS Management Console' → 第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] ✅ 直接确认为第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息]  智能检测到当前在第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🎯 找到匹配链接: 'Go to the AWS Management Console' → 第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] ✅ 直接确认为第9页 (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 智能检测到当前在第9页，继续执行... (进度: 100%)
2025-08-03 07:33:28 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 07:33:31 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-03 07:33:31 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-03 07:33:31 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-03 07:33:33 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-03 07:33:33 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-03 07:33:33 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-03 07:33:33 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-03 07:33:33 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-03 07:33:33 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:33:33 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:33:33 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:33:33 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:33:33 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-03 07:33:33 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 07:33:33 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-03 07:33:33 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-03 07:33:33 [信息] 注册完成（密钥提取失败）
2025-08-03 07:33:33 线程1：[信息] 已继续
2025-08-03 07:33:33 [信息] 线程1已继续
2025-08-03 07:33:33 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-03 07:33:33 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-03 07:33:33 [信息] 已完成数据移除: <EMAIL>
2025-08-03 07:33:33 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-03 07:33:33 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-03 07:33:33 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 07:33:34 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 07:33:34 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 07:33:34 线程2：[信息] 已暂停
2025-08-03 07:33:34 [信息] 线程2已暂停
2025-08-03 07:33:34 [信息] 线程2已暂停
2025-08-03 07:33:35 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-03 07:33:35 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:33:35 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:33:35 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息] 🎯 找到匹配链接: 'Go to the AWS Management Console' → 第9页 (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息] ✅ 直接确认为第9页 (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息]  智能检测到当前在第9页 (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息] 智能检测到当前在第9页，开始智能处理... (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息] 🔑 第9页：点击控制台链接并开始AWS访问密钥提取流程... (进度: 100%)
2025-08-03 07:33:37 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 07:33:39 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-03 07:33:39 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-03 07:33:40 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-03 07:33:40 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-03 07:33:40 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-03 07:33:40 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-03 07:33:40 [信息] 成功点击更多按钮
2025-08-03 07:33:41 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-03 07:33:43 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-03 07:33:43 [信息] 成功点击账户信息按钮
2025-08-03 07:33:44 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-03 07:33:44 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-03 07:33:44 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-03 07:33:44 [信息] 成功定位到'安全凭证'链接
2025-08-03 07:33:48 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-03 07:33:48 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-03 07:33:48 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-03 07:33:48 [信息] 成功点击更多按钮
2025-08-03 07:33:49 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-03 07:33:50 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-03 07:33:50 [信息] 成功点击账户信息按钮
2025-08-03 07:33:50 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-03 07:33:50 [信息] 成功点击'安全凭证'链接
2025-08-03 07:33:50 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-03 07:33:51 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-03 07:33:51 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-03 07:33:51 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-03 07:33:51 [信息] 成功定位到'安全凭证'链接
2025-08-03 07:33:53 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-03 07:33:53 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-03 07:33:53 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-03 07:33:53 [信息] 检测到账单问题，开始处理
2025-08-03 07:33:53 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-03 07:33:53 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:53 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:53 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:53 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:53 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3ZO08Q39v ③AWS密码：Do2hNc7T ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:53 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-03 07:33:53 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-03 07:33:53 [信息] 注册完成 - 账单提示处理
2025-08-03 07:33:53 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 07:33:53 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-03 07:33:53 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-03 07:33:53 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-03 07:33:53 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-03 07:33:53 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 07:33:53 线程1：[信息] 已继续
2025-08-03 07:33:53 [信息] 线程1已继续
2025-08-03 07:33:56 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-03 07:33:56 [信息] 成功点击'安全凭证'链接
2025-08-03 07:33:56 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-03 07:33:58 线程2：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-03 07:33:58 线程2：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-03 07:33:58 线程2：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-03 07:33:58 [信息] 检测到账单问题，开始处理
2025-08-03 07:33:58 线程2：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-03 07:33:58 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:58 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:58 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:58 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:58 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：72QYe0Zd ③AWS密码：D9vuQzxi ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:33:58 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:33:58 [信息] 多线程状态已重置
2025-08-03 07:33:58 线程2：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-03 07:33:58 线程2：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-03 07:33:58 [信息] 注册完成 - 账单提示处理
2025-08-03 07:33:58 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 07:33:58 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-03 07:33:58 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-03 07:33:58 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-03 07:33:58 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-03 07:33:58 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:33:58 [信息] 多线程状态已重置
2025-08-03 07:33:58 线程2：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-03 07:33:58 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:33:58 [信息] 多线程状态已重置
2025-08-03 07:33:58 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 07:33:58 线程2：[信息] 已继续
2025-08-03 07:33:58 [信息] 线程2已继续
2025-08-03 07:34:10 [信息] 多线程窗口引用已清理
2025-08-03 07:34:10 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 07:34:10 [信息] 多线程管理窗口正在关闭
2025-08-03 07:34:12 [信息] 程序正在退出，开始清理工作...
2025-08-03 07:34:12 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 07:34:12 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 07:34:12 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 07:34:12 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 07:34:12 [信息] 程序退出清理工作完成



第二批2025-08-03 07:47:54 [信息] AWS自动注册工具启动
2025-08-03 07:47:54 [信息] 程序版本: 1.0.0.0
2025-08-03 07:47:54 [信息] 启动时间: 2025-08-03 07:47:54
2025-08-03 07:47:54 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 07:47:54 [信息] 线程数量已选择: 1
2025-08-03 07:47:54 [信息] 线程数量选择初始化完成
2025-08-03 07:47:54 [信息] 程序初始化完成
2025-08-03 07:47:56 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 07:47:59 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 07:47:59 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 07:47:59 [信息] 成功加载 10 条数据
2025-08-03 07:48:01 [信息] 线程数量已选择: 2
2025-08-03 07:48:08 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 07:48:08 [信息] 开始启动多线程注册，线程数量: 2
2025-08-03 07:48:08 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 10
2025-08-03 07:48:08 [信息] 所有线程已停止并清理
2025-08-03 07:48:08 [信息] 正在初始化多线程服务...
2025-08-03 07:48:08 [信息] 榴莲手机API服务已初始化
2025-08-03 07:48:08 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-03 07:48:08 [信息] 多线程服务初始化完成
2025-08-03 07:48:08 [信息] 数据分配完成：共10条数据分配给2个线程
2025-08-03 07:48:08 [信息] 线程1分配到5条数据
2025-08-03 07:48:08 [信息] 线程2分配到5条数据
2025-08-03 07:48:08 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:48:08 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:48:08 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 07:48:08 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:48:08 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_001, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=32 GB
2025-08-03 07:48:08 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 07:48:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 07:48:08 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:48:08 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:48:08 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 07:48:08 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:48:08 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=6 GB
2025-08-03 07:48:08 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-03 07:48:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:48:08 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 07:48:08 [信息] 多线程注册启动成功，共2个线程
2025-08-03 07:48:08 线程1：[信息] 开始启动注册流程
2025-08-03 07:48:08 线程2：[信息] 开始启动注册流程
2025-08-03 07:48:08 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 07:48:08 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-03 07:48:08 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:48:08 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:48:08 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:48:08 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:48:08 [信息] 多线程管理窗口已初始化
2025-08-03 07:48:08 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:48:08 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 07:48:08 [信息] 多线程管理窗口已打开
2025-08-03 07:48:08 [信息] 多线程注册启动成功，共2个线程
2025-08-03 07:48:09 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:48:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 07:48:09 线程1：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 07:48:09 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 07:48:09 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-03 07:48:09 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:48:09 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:48:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 07:48:09 线程2：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 07:48:09 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 07:48:09 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-03 07:48:09 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:48:11 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:48:11 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:48:13 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 07:48:13 线程2：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:48:13 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 07:48:13 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 10核 (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=6 GB
2025-08-03 07:48:13 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 07:48:13 线程1：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:48:13 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 07:48:13 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 07:48:15 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:48:15 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_001, CPU: 16核 (进度: 0%)
2025-08-03 07:48:15 [信息] 浏览器指纹注入: Canvas=canvas_fp_001, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=32 GB
2025-08-03 07:48:16 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:48:17 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 10
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1946x989
   • 可用区域: 1946x949

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.41
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:48:17 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 10    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1946x989    • 可用区域: 1946x949   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.41    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:48:17 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 2087x1150
   • 可用区域: 2087x1110

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.30
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:48:17 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 2087x1150    • 可用区域: 2087x1110   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.30    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 07:48:17 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 07:48:17 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 07:48:17 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:48:17 线程2：[信息] 浏览器启动成功
2025-08-03 07:48:17 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:48:17 线程1：[信息] 浏览器启动成功
2025-08-03 07:48:17 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:48:17 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:48:17 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:48:17 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:48:18 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-03 07:48:18 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-03 07:48:18 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:48:32 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 07:48:32 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 07:48:32 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 07:48:32 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 07:48:32 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 07:48:32 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 07:48:32 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 07:48:32 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 07:48:32 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 07:48:32 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 07:48:33 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 07:48:33 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 07:48:36 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 07:48:36 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 07:48:36 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 07:48:36 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 07:48:40 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 07:48:40 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 07:48:40 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:48:43 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35092 字节 (进度: 100%)
2025-08-03 07:48:43 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35092字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:48:43 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:48:44 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35374 字节 (进度: 100%)
2025-08-03 07:48:44 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35374字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:48:44 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:48:46 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"crwpsy"},"taskId":"3a79ae7c-6ffb-11f0-a1bf-5254008382c7"} (进度: 100%)
2025-08-03 07:48:46 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xt7fhr"},"taskId":"3a6f31a4-6ffb-11f0-9058-7234de99a3d9"} (进度: 100%)
2025-08-03 07:48:46 线程2：[信息] [信息] 第一页第1次识别结果: xt7fhr → 转换为小写: xt7fhr (进度: 100%)
2025-08-03 07:48:46 线程1：[信息] [信息] 第一页第1次识别结果: crwpsy → 转换为小写: crwpsy (进度: 100%)
2025-08-03 07:48:46 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:48:46 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:48:46 线程1：[信息] [信息] 已填入验证码: crwpsy (进度: 100%)
2025-08-03 07:48:46 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:48:46 线程2：[信息] [信息] 已填入验证码: xt7fhr (进度: 100%)
2025-08-03 07:48:46 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:48:48 线程1：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 07:48:48 线程1：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-03 07:48:48 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:48:48 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 07:48:48 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 07:48:48 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 07:48:49 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 07:48:49 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 07:48:49 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 07:48:49 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 07:48:49 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:48:49 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 07:48:49 [信息] [线程2] 已删除旧的响应文件
2025-08-03 07:48:49 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-03 07:48:50 线程1：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:48:51 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 07:48:51 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:48:51
2025-08-03 07:48:55 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 07:48:55 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:48:55
2025-08-03 07:48:55 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31050 字节 (进度: 100%)
2025-08-03 07:48:55 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31050字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:48:55 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:48:56 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"rgp7cb"},"taskId":"4032d41a-6ffb-11f0-bf43-7234de99a3d9"} (进度: 100%)
2025-08-03 07:48:56 线程1：[信息] [信息] 第一页第2次识别结果: rgp7cb → 转换为小写: rgp7cb (进度: 100%)
2025-08-03 07:48:56 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:48:56 线程1：[信息] [信息] 已填入验证码: rgp7cb (进度: 100%)
2025-08-03 07:48:56 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:48:58 线程1：[信息] [信息] 第一页第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 07:48:58 线程1：[信息] [信息] 第一页第3次失败，等待新验证码... (进度: 100%)
2025-08-03 07:48:58 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 07:48:58 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:48:58
2025-08-03 07:49:00 线程1：[信息] [信息] 第一页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:49:01 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 07:49:01 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:49:01
2025-08-03 07:49:03 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31346 字节 (进度: 100%)
2025-08-03 07:49:03 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31346字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:49:03 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:49:04 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"dp72n8"},"taskId":"44fb05da-6ffb-11f0-bc85-2e23c0b40dc6"} (进度: 100%)
2025-08-03 07:49:04 线程1：[信息] [信息] 第一页第3次识别结果: dp72n8 → 转换为小写: dp72n8 (进度: 100%)
2025-08-03 07:49:04 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:49:04 线程1：[信息] [信息] 已填入验证码: dp72n8 (进度: 100%)
2025-08-03 07:49:04 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:49:04 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-08-03 07:49:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 07:49:04
2025-08-03 07:49:06 线程1：[信息] [信息] 第一页第3次图形验证码识别成功 (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 07:49:06 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 07:49:06 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:49:06 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 07:49:06 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 07:49:07 [信息] [线程2] 邮箱验证码获取成功: 792503，立即停止重复请求
2025-08-03 07:49:07 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-03 07:49:07 [信息] [线程2] 已清理响应文件
2025-08-03 07:49:07 线程2：[信息] [信息] 验证码获取成功: 792503，正在自动填入... (进度: 25%)
2025-08-03 07:49:07 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 07:49:07 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 07:49:07 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 07:49:07 [信息] 线程2完成第二页事件已处理
2025-08-03 07:49:07 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-03 07:49:07 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 07:49:07 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-03 07:49:07 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-03 07:49:08 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+529831074283","+524441952382"]}
2025-08-03 07:49:08 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-03 07:49:08 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-03 07:49:08 [信息] 线程1分配榴莲手机号码: +529831074283
2025-08-03 07:49:08 [信息] 线程2分配榴莲手机号码: +524441952382
2025-08-03 07:49:08 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-03 07:49:08 [信息] 批量获取2个手机号码成功
2025-08-03 07:49:08 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 07:49:08 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:49:08
2025-08-03 07:49:10 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-03 07:49:10 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-03 07:49:10 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:49:10 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:49:10 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-03 07:49:11 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 07:49:11 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:49:11
2025-08-03 07:49:11 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-03 07:49:13 [信息] [线程1] 邮箱验证码获取成功: 496584，立即停止重复请求
2025-08-03 07:49:13 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 07:49:13 [信息] [线程1] 已清理响应文件
2025-08-03 07:49:13 线程1：[信息] [信息] 验证码获取成功: 496584，正在自动填入... (进度: 25%)
2025-08-03 07:49:13 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 07:49:13 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 07:49:13 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 07:49:13 [信息] 线程1完成第二页事件已处理
2025-08-03 07:49:13 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-03 07:49:13 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 07:49:14 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-03 07:49:14 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-03 07:49:14 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-03 07:49:16 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-03 07:49:16 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-03 07:49:16 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:49:18 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:49:18 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-03 07:49:19 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-03 07:49:19 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-03 07:49:19 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-03 07:49:24 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-03 07:49:24 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-03 07:49:24 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-03 07:49:28 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-03 07:49:28 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-03 07:49:34 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-03 07:49:34 [信息] 线程2获取已分配的榴莲手机号码: +524441952382
2025-08-03 07:49:34 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +524441952382 (进度: 38%)
2025-08-03 07:49:36 线程2：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 38%)
2025-08-03 07:49:36 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-03 07:49:38 线程2：[信息] [信息] 已选择国家: Vietnam (进度: 38%)
2025-08-03 07:49:38 线程2：[信息] [信息] 已成功选择国家: Vietnam (进度: 38%)
2025-08-03 07:49:38 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 07:49:38 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 07:49:42 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-03 07:49:42 [信息] 线程1获取已分配的榴莲手机号码: +529831074283
2025-08-03 07:49:42 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529831074283 (进度: 38%)
2025-08-03 07:49:43 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-03 07:49:43 线程1：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 38%)
2025-08-03 07:49:43 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-03 07:49:45 线程1：[信息] [信息] 已选择国家: Vietnam (进度: 38%)
2025-08-03 07:49:45 线程1：[信息] [信息] 已成功选择国家: Vietnam (进度: 38%)
2025-08-03 07:49:45 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 07:49:45 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 07:49:46 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-03 07:49:46 线程2：[信息] [信息] 已自动获取并填入手机号码: +524441952382 (进度: 38%)
2025-08-03 07:49:47 线程2：[信息] [信息] 使用已获取的手机号码: +524441952382（保存本地号码: +524441952382） (进度: 38%)
2025-08-03 07:49:47 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-03 07:49:49 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-03 07:49:49 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-03 07:49:49 线程1：[信息] [信息] 已自动获取并填入手机号码: +529831074283 (进度: 38%)
2025-08-03 07:49:50 线程1：[信息] [信息] 使用已获取的手机号码: +529831074283（保存本地号码: +529831074283） (进度: 38%)
2025-08-03 07:49:51 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-03 07:49:51 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-03 07:49:52 线程2：[信息] [信息] 正在选择月份: February (进度: 38%)
2025-08-03 07:49:52 线程2：[信息] [信息] 已选择月份（标准选项）: February (进度: 38%)
2025-08-03 07:49:53 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-03 07:49:54 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-03 07:49:54 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-03 07:49:55 线程1：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-03 07:49:55 线程1：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-03 07:49:56 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-03 07:49:56 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-03 07:49:56 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 07:49:56 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-03 07:49:56 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-03 07:49:57 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-03 07:49:57 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-03 07:49:57 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 07:50:00 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 07:50:01 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-03 07:50:02 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 07:50:02 线程2：[信息] [信息] 已清空并重新填写手机号码: +524441952382 (进度: 38%)
2025-08-03 07:50:02 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-03 07:50:03 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-03 07:50:03 线程1：[信息] [信息] 已清空并重新填写手机号码: +529831074283 (进度: 38%)
2025-08-03 07:50:03 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-03 07:50:04 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-03 07:50:04 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:50:04 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 07:50:04 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 07:50:04 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:50:05 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-03 07:50:05 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:50:05 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 07:50:05 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 07:50:05 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:50:07 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:50:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:50:08 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:50:08 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:50:10 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34661 字节 (进度: 100%)
2025-08-03 07:50:10 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34661字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:50:10 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:50:12 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2t38my"},"taskId":"6d603f40-6ffb-11f0-bc7e-2e23c0b40dc6"} (进度: 100%)
2025-08-03 07:50:12 线程2：[信息] [信息] 第六页第1次识别结果: 2t38my → 转换为小写: 2t38my (进度: 100%)
2025-08-03 07:50:12 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34541 字节 (进度: 100%)
2025-08-03 07:50:12 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34541字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:50:12 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:50:12 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:50:12 线程2：[信息] [信息] 第六页已填入验证码: 2t38my (进度: 100%)
2025-08-03 07:50:13 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:50:13 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pg5rmp"},"taskId":"6e8614e4-6ffb-11f0-bc85-2e23c0b40dc6"} (进度: 100%)
2025-08-03 07:50:13 线程1：[信息] [信息] 第六页第1次识别结果: pg5rmp → 转换为小写: pg5rmp (进度: 100%)
2025-08-03 07:50:13 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:50:14 线程1：[信息] [信息] 第六页已填入验证码: pg5rmp (进度: 100%)
2025-08-03 07:50:14 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:50:16 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:50:16 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:50:17 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:50:17 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:50:19 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:50:20 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:50:22 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:50:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:50:22 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:50:22 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:50:23 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:50:23 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:50:23 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:50:23 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:50:27 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-03 07:50:27 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 07:50:27 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:50:27 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:50:28 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 07:50:28 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 07:50:28 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:50:28 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:50:29 线程2：[信息] [信息] 线程2验证码获取成功: 5126 (进度: 100%)
2025-08-03 07:50:29 [信息] 线程2手机号码已加入释放队列: +524441952382 (原因: 获取验证码成功)
2025-08-03 07:50:29 线程2：[信息] [信息] 线程2验证码获取成功: 5126，立即填入验证码... (进度: 100%)
2025-08-03 07:50:29 线程1：[信息] [信息] 线程1验证码获取成功: 6420 (进度: 100%)
2025-08-03 07:50:29 [信息] 线程1手机号码已加入释放队列: +529831074283 (原因: 获取验证码成功)
2025-08-03 07:50:29 线程1：[信息] [信息] 线程1验证码获取成功: 6420，立即填入验证码... (进度: 100%)
2025-08-03 07:50:29 线程1：[信息] [信息] 线程1已自动填入手机验证码: 6420 (进度: 100%)
2025-08-03 07:50:29 线程2：[信息] [信息] 线程2已自动填入手机验证码: 5126 (进度: 100%)
2025-08-03 07:50:30 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-03 07:50:30 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-03 07:50:30 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 07:50:30 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 07:50:34 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-03 07:50:34 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-03 07:50:34 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-03 07:50:34 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-03 07:50:34 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-03 07:50:35 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-03 07:50:35 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-03 07:50:35 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-03 07:50:38 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-03 07:50:38 [信息] 开始释放2个手机号码
2025-08-03 07:50:38 [信息] [手机API] 开始批量释放2个手机号码
2025-08-03 07:50:38 [信息] [手机API] 释放手机号码: +524441952382
2025-08-03 07:50:38 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-03 07:50:38 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 07:50:38 [信息] [手机API] 手机号码释放成功: +524441952382
2025-08-03 07:50:38 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-03 07:50:38 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 07:50:39 [信息] [手机API] 释放手机号码: +529831074283
2025-08-03 07:50:39 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-03 07:50:39 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-03 07:50:39 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-03 07:50:39 [信息] [手机API] 手机号码释放成功: +529831074283
2025-08-03 07:50:40 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-03 07:50:40 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-03 07:50:41 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-03 07:50:41 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-03 07:50:42 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-03 07:50:49 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-03 07:50:49 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-03 07:50:50 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-03 07:50:50 [信息] 成功点击更多按钮
2025-08-03 07:50:50 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-03 07:50:50 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-03 07:50:51 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-03 07:50:51 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-03 07:50:51 [信息] 成功点击账户信息按钮
2025-08-03 07:50:51 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-03 07:50:51 [信息] 成功点击更多按钮
2025-08-03 07:50:52 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-03 07:50:52 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-03 07:50:52 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-03 07:50:52 [信息] 成功定位到'安全凭证'链接
2025-08-03 07:50:53 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-03 07:50:54 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-03 07:50:54 [信息] 成功点击账户信息按钮
2025-08-03 07:50:55 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-03 07:50:55 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-03 07:50:55 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-03 07:50:55 [信息] 成功定位到'安全凭证'链接
2025-08-03 07:50:58 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-03 07:50:58 [信息] 成功点击'安全凭证'链接
2025-08-03 07:50:58 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-03 07:51:00 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-03 07:51:00 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-03 07:51:00 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-03 07:51:00 [信息] 检测到账单问题，开始处理
2025-08-03 07:51:00 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-03 07:51:00 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:00 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:00 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:01 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:01 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：4ZjFH6e19 ③AWS密码：6i7ubFxM ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:01 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-03 07:51:01 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-03 07:51:01 [信息] 注册完成 - 账单提示处理
2025-08-03 07:51:01 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 07:51:01 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-03 07:51:01 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-03 07:51:01 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-03 07:51:01 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-03 07:51:01 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-03 07:51:01 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 07:51:01 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-03 07:51:01 [信息] 成功点击'安全凭证'链接
2025-08-03 07:51:01 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-03 07:51:04 线程2：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-03 07:51:04 线程2：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-03 07:51:04 线程2：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-03 07:51:04 [信息] 检测到账单问题，开始处理
2025-08-03 07:51:04 线程2：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-03 07:51:04 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:04 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:04 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:04 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:04 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：I6a3L6M7 ③AWS密码：vTVFf5o2 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 07:51:04 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:51:04 [信息] 多线程状态已重置
2025-08-03 07:51:04 线程2：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-03 07:51:04 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-03 07:51:04 线程2：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-03 07:51:04 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-03 07:51:04 [信息] 注册完成 - 账单提示处理
2025-08-03 07:51:04 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-03 07:51:04 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-03 07:51:04 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 07:51:05 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:51:05 [信息] 多线程状态已重置
2025-08-03 07:51:05 线程2：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-03 07:51:05 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:51:05 [信息] 多线程状态已重置
2025-08-03 07:51:05 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 07:51:05 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:51:05 [信息] 多线程状态已重置
2025-08-03 07:51:30 [信息] 多线程窗口引用已清理
2025-08-03 07:51:30 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 07:51:30 [信息] 多线程管理窗口正在关闭
2025-08-03 07:52:36 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 07:52:36 [信息] 开始启动多线程注册，线程数量: 2
2025-08-03 07:52:36 [信息] 多线程管理器已重新创建
2025-08-03 07:52:36 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 8
2025-08-03 07:52:36 [信息] 所有线程已停止并清理
2025-08-03 07:52:36 [信息] 正在初始化多线程服务...
2025-08-03 07:52:36 [信息] 榴莲手机API服务已初始化
2025-08-03 07:52:36 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-03 07:52:36 [信息] 多线程服务初始化完成
2025-08-03 07:52:36 [信息] 数据分配完成：共8条数据分配给2个线程
2025-08-03 07:52:36 [信息] 线程1分配到4条数据
2025-08-03 07:52:36 [信息] 线程2分配到4条数据
2025-08-03 07:52:36 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:52:36 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:52:36 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 07:52:36 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:52:36 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=12 GB
2025-08-03 07:52:36 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 07:52:36 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 07:52:36 [信息] 屏幕工作区域: 1280x672
2025-08-03 07:52:36 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 07:52:36 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 07:52:36 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:52:36 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=48 GB
2025-08-03 07:52:36 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-03 07:52:36 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 07:52:36 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 07:52:36 [信息] 多线程注册启动成功，共2个线程
2025-08-03 07:52:36 [信息] 多线程管理窗口已初始化
2025-08-03 07:52:36 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:52:36 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 07:52:36 [信息] 多线程管理窗口已打开
2025-08-03 07:52:36 [信息] 多线程注册启动成功，共2个线程
2025-08-03 07:52:37 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:52:37 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 07:52:37 线程1：[信息] 开始启动注册流程
2025-08-03 07:52:37 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-03 07:52:37 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:52:37 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:52:38 [信息] UniformGrid列数已更新为: 1
2025-08-03 07:52:38 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 07:52:38 线程2：[信息] 开始启动注册流程
2025-08-03 07:52:38 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-03 07:52:38 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 07:52:38 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 07:52:39 线程1：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 07:52:39 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 07:52:39 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-03 07:52:39 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:52:39 线程2：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 07:52:39 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 07:52:39 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-03 07:52:39 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 07:52:41 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:52:41 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 07:52:43 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 07:52:43 线程1：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:52:43 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 07:52:43 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 07:52:43 线程2：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 07:52:43 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 07:52:43 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 12核 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=12 GB
2025-08-03 07:52:43 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 2核 (进度: 0%)
2025-08-03 07:52:43 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=48 GB
2025-08-03 07:52:44 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:52:44 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 07:52:46 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: 0

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9E0F1A2B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 0

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1869x1018
   • 可用区域: 1869x978

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.90
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:52:46 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: 0   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9E0F1A2B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 0   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1869x1018    • 可用区域: 1869x978   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.90    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:52:46 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 48 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1808x1034
   • 可用区域: 1808x994

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: E7F8A9B0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.89
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 07:52:46 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 48 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1808x1034    • 可用区域: 1808x994   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: E7F8A9B0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.89    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 07:52:46 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 07:52:46 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 07:52:46 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:52:46 线程2：[信息] 浏览器启动成功
2025-08-03 07:52:46 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 07:52:46 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:52:46 线程1：[信息] 浏览器启动成功
2025-08-03 07:52:46 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 07:52:46 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:52:46 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:52:46 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 07:52:46 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 07:52:46 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:52:46 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:52:46 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 07:52:46 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:52:46 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 07:52:46 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:52:46 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 07:52:46 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 07:52:48 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 07:52:48 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 07:53:02 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 07:53:02 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 07:53:02 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 07:53:02 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 07:53:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 07:53:02 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 07:53:05 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 07:53:05 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 07:53:05 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:53:08 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34864 字节 (进度: 100%)
2025-08-03 07:53:08 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34864字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:53:08 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:53:09 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mrg852"},"taskId":"d7569f48-6ffb-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 07:53:09 线程1：[信息] [信息] 第一页第1次识别结果: mrg852 → 转换为小写: mrg852 (进度: 100%)
2025-08-03 07:53:09 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:53:09 线程1：[信息] [信息] 已填入验证码: mrg852 (进度: 100%)
2025-08-03 07:53:09 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:53:11 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:53:11 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 07:53:11 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 07:53:11 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 07:53:12 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 07:53:12 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 07:53:12 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 07:53:12 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 07:53:12 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 07:53:12 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 07:53:12 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:53:14 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 07:53:14 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:53:14
2025-08-03 07:53:17 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 07:53:17 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 07:53:17
2025-08-03 07:53:18 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-03 07:53:18 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-03 07:53:18 [信息] 第一页相关失败，数据保持不动
2025-08-03 07:53:18 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-03 07:53:18 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 07:53:19 [信息] [线程1] 邮箱验证码获取成功: 049464，立即停止重复请求
2025-08-03 07:53:19 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 07:53:19 [信息] [线程1] 已清理响应文件
2025-08-03 07:53:19 线程1：[信息] [信息] 验证码获取成功: 049464，正在自动填入... (进度: 25%)
2025-08-03 07:53:19 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 07:53:19 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 07:53:19 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 07:53:19 [信息] 线程1完成第二页事件已处理
2025-08-03 07:53:19 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-03 07:53:19 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 07:53:22 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-03 07:53:22 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-03 07:53:22 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:53:22 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 07:53:22 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-03 07:53:23 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-03 07:53:26 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-03 07:53:26 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-03 07:53:26 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-03 07:53:31 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-03 07:53:31 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-03 07:53:48 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-03 07:53:48 [信息] 线程1获取已分配的榴莲手机号码: +529831074283
2025-08-03 07:53:48 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529831074283 (进度: 38%)
2025-08-03 07:53:50 线程1：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 38%)
2025-08-03 07:53:51 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] 所有自动线程已停止 (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] 已暂停
2025-08-03 07:54:16 [信息] 线程1已暂停
2025-08-03 07:54:16 [信息] 线程1已暂停
2025-08-03 07:54:16 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 4 (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-08-03 07:54:16 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-03 07:54:16 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-03 07:54:16 线程1：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-03 07:54:16 [信息] 线程1获取已分配的榴莲手机号码: +529831074283
2025-08-03 07:54:16 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529831074283 (进度: 100%)
2025-08-03 07:54:17 线程1：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 100%)
2025-08-03 07:54:18 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-03 07:54:18 线程1：[信息] [信息] 已选择国家: Vietnam (进度: 100%)
2025-08-03 07:54:18 线程1：[信息] [信息] 已成功选择国家: Vietnam (进度: 100%)
2025-08-03 07:54:18 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 07:54:18 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 07:54:19 线程1：[信息] [信息] 已选择国家: Vietnam (进度: 100%)
2025-08-03 07:54:19 线程1：[信息] [信息] 已成功选择国家: Vietnam (进度: 100%)
2025-08-03 07:54:19 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 07:54:23 线程1：[信息] [信息] 自动选择国家代码失败，请手动选择+52 (进度: 100%)
2025-08-03 07:54:24 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-03 07:54:24 线程1：[信息] [信息] 已自动获取并填入手机号码: +529831074283 (进度: 100%)
2025-08-03 07:54:24 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 07:54:25 线程1：[信息] [信息] 使用已获取的手机号码: +529831074283（保存本地号码: +529831074283） (进度: 100%)
2025-08-03 07:54:25 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-03 07:54:25 线程1：[信息] [信息] 自动选择国家代码失败，请手动选择+52 (进度: 100%)
2025-08-03 07:54:27 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-03 07:54:27 线程1：[信息] [信息] 已自动获取并填入手机号码: +529831074283 (进度: 100%)
2025-08-03 07:54:28 线程1：[信息] [信息] 使用已获取的手机号码: +529831074283（保存本地号码: +529831074283） (进度: 100%)
2025-08-03 07:54:29 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-03 07:54:29 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-03 07:54:32 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-03 07:54:39 线程1：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-03 07:54:39 线程1：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-03 07:54:41 线程1：[信息] [信息] 标准选项方法失败，尝试其他方法... (进度: 100%)
2025-08-03 07:54:41 线程1：[信息] [信息] 标准选项方法失败，尝试其他方法... (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] 已暂停
2025-08-03 07:54:42 [信息] 线程1已暂停
2025-08-03 07:54:42 [信息] 线程1已暂停
2025-08-03 07:54:42 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-03 07:54:42 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-03 07:54:43 线程1：[信息] [信息] 下拉菜单上下文方法失败，尝试精确匹配... (进度: 100%)
2025-08-03 07:54:43 线程1：[信息] [信息] 下拉菜单上下文方法失败，尝试精确匹配... (进度: 100%)
2025-08-03 07:54:46 线程1：[信息] [信息] 精确匹配方法失败，尝试通用方法... (进度: 100%)
2025-08-03 07:54:46 线程1：[信息] [信息] 精确匹配方法失败，尝试通用方法... (进度: 100%)
2025-08-03 07:54:49 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 07:54:49 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 07:54:49 线程1：[信息] 已暂停
2025-08-03 07:54:49 [信息] 线程1已暂停
2025-08-03 07:54:49 [信息] 线程1已暂停
2025-08-03 07:54:50 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-03 07:54:50 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-03 07:55:13 线程1：[信息] [信息] 继续注册失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Expiration date Month" }) (进度: 100%)
2025-08-03 07:55:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:13 [信息] 多线程状态已重置
2025-08-03 07:55:13 线程1：[信息] 已继续
2025-08-03 07:55:13 [信息] 线程1已继续
2025-08-03 07:55:16 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:16 [信息] 多线程状态已重置
2025-08-03 07:55:16 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: 选择月份失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByText("August").First，但手机号码已保存 (进度: 100%)
2025-08-03 07:55:16 线程1：[信息] 已继续
2025-08-03 07:55:16 [信息] 线程1已继续
2025-08-03 07:55:16 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:16 [信息] 多线程状态已重置
2025-08-03 07:55:16 线程1：[信息] [信息] 执行第五页失败: 第五页执行失败: 选择月份失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByText("August").First，但手机号码已保存 (进度: 100%)
2025-08-03 07:55:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:20 [信息] 多线程状态已重置
2025-08-03 07:55:20 线程1：[信息] [信息] 继续注册失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Expiration date Month" }) (进度: 100%)
2025-08-03 07:55:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:20 [信息] 多线程状态已重置
2025-08-03 07:55:20 线程1：[信息] 已继续
2025-08-03 07:55:20 [信息] 线程1已继续
2025-08-03 07:55:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:55:54 [信息] 多线程状态已重置
2025-08-03 07:55:54 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] 已暂停
2025-08-03 07:55:54 [信息] 线程1已暂停
2025-08-03 07:55:54 [信息] 线程1已暂停
2025-08-03 07:55:54 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-03 07:55:54 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-03 07:55:55 线程1：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-03 07:55:55 线程1：[信息] [信息] 已选择月份（标准选项）: August (进度: 100%)
2025-08-03 07:55:56 线程1：[信息] [信息] 正在选择年份: 2025 (进度: 100%)
2025-08-03 07:55:57 线程1：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 100%)
2025-08-03 07:55:59 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-03 07:55:59 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-03 07:55:59 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 07:56:04 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 07:56:05 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-03 07:56:05 线程1：[信息] [信息] 已清空并重新填写手机号码: +529831074283 (进度: 100%)
2025-08-03 07:56:06 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-03 07:56:08 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 07:56:08 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:56:08 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 07:56:08 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 07:56:08 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:56:11 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:56:11 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:56:21 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35325 字节 (进度: 100%)
2025-08-03 07:56:21 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35325字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:56:21 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:56:22 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fp6xfn"},"taskId":"4a31d9e2-6ffc-11f0-bc85-2e23c0b40dc6"} (进度: 100%)
2025-08-03 07:56:22 线程1：[信息] [信息] 第六页第1次识别结果: fp6xfn → 转换为小写: fp6xfn (进度: 100%)
2025-08-03 07:56:22 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:56:22 线程1：[信息] [信息] 第六页已填入验证码: fp6xfn (进度: 100%)
2025-08-03 07:56:22 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:56:25 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:56:25 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:56:29 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:56:32 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:56:32 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:56:32 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:56:32 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:56:37 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 07:56:37 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 07:56:37 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:56:37 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:56:38 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:56:38 线程1：[信息] [信息] 线程1第1次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:56:38 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:56:46 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-03 07:56:46 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:56:46 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:56:46 线程1：[信息] [信息] 线程1第2次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:56:46 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:56:54 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-03 07:56:54 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:56:55 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:56:55 线程1：[信息] [信息] 线程1第3次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:56:55 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:57:03 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-03 07:57:03 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:57:03 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:57:03 线程1：[信息] [信息] 线程1第4次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:57:03 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:57:11 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-03 07:57:11 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:57:12 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:57:12 线程1：[信息] [信息] 线程1第5次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:57:12 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:57:20 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-03 07:57:20 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:57:20 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:57:20 线程1：[信息] [信息] 线程1第6次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:57:20 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:57:28 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-03 07:57:28 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:57:28 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:57:28 线程1：[信息] [信息] 线程1第7次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:57:28 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 07:57:36 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-03 07:57:36 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:57:37 [警告] [榴莲API] 获取验证码业务失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员
2025-08-03 07:57:37 线程1：[信息] [信息] 线程1第8次尝试失败: 获取验证码失败: 验证码获取失败，请查询数据列表，或联系管理员 (进度: 100%)
2025-08-03 07:57:37 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-08-03 07:57:37 [信息] 线程1手机号码已加入释放队列: +529831074283 (原因: 验证码获取失败)
2025-08-03 07:57:37 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-03 07:57:37 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-03 07:57:37 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-03 07:57:37 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-03 07:57:37 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-03 07:57:38 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 07:57:38 [信息] 开始释放1个手机号码
2025-08-03 07:57:38 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 07:57:38 [信息] [手机API] 释放手机号码: +529831074283
2025-08-03 07:57:38 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-03 07:57:38 [信息] [手机API] 手机号码释放成功: +529831074283
2025-08-03 07:57:38 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 07:57:38 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 07:57:40 线程1：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-03 07:57:40 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-03 07:57:41 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-03 07:57:41 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-03 07:57:41 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-03 07:57:42 线程1：[信息] [信息] 后台获取新手机号码成功: +************，已保存到注册数据 (进度: 100%)
2025-08-03 07:57:42 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-03 07:57:42 线程1：[信息] [信息] 已填入新手机号码: +************ (进度: 100%)
2025-08-03 07:57:42 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-03 07:57:44 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 07:57:44 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 07:57:45 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 07:57:45 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 07:57:48 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 07:57:48 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 07:57:51 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35029 字节 (进度: 100%)
2025-08-03 07:57:51 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35029字节，复杂度符合要求 (进度: 100%)
2025-08-03 07:57:51 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 07:57:52 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"z6c8nb"},"taskId":"7fd20f04-6ffc-11f0-8c61-861c57ef4db4"} (进度: 100%)
2025-08-03 07:57:52 线程1：[信息] [信息] 第六页第1次识别结果: z6c8nb → 转换为小写: z6c8nb (进度: 100%)
2025-08-03 07:57:52 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 07:57:52 线程1：[信息] [信息] 第六页已填入验证码: z6c8nb (进度: 100%)
2025-08-03 07:57:52 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 07:57:55 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 07:57:55 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 07:57:58 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 07:58:01 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 07:58:01 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:58:01 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 07:58:01 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 07:58:06 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1验证码获取成功: 1340 (进度: 100%)
2025-08-03 07:58:06 [信息] 线程1手机号码已加入释放队列: +************ (原因: 获取验证码成功)
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1验证码获取成功: 1340，立即填入验证码... (进度: 100%)
2025-08-03 07:58:06 线程1：[信息] [信息] 线程1已自动填入手机验证码: 1340 (进度: 100%)
2025-08-03 07:58:07 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-03 07:58:08 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 07:58:08 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 07:58:08 [信息] 开始释放1个手机号码
2025-08-03 07:58:08 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 07:58:08 [信息] [手机API] 释放手机号码: +************
2025-08-03 07:58:08 [信息] [手机API] 手机号码释放成功: +************
2025-08-03 07:58:08 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 07:58:08 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 07:58:11 线程1：[信息] [信息] 检测到支付信息错误提示: There was a problem with your payment information. To continue, sign in to your account  and update your payment information. Then you can continue signing up. If this problem persists, contact AWS Customer Support . (进度: 100%)
2025-08-03 07:58:11 线程1：[信息] [信息] 线程1检测到支付信息错误，注册失败 (进度: 100%)
2025-08-03 07:58:11 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误 (进度: 100%)
2025-08-03 07:58:11 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误
2025-08-03 07:58:11 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误
2025-08-03 07:58:11 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误
2025-08-03 07:58:11 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误
2025-08-03 07:58:11 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：H49R26BE7Z ③AWS密码：3Mvujn2x ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //支付信息错误
2025-08-03 07:58:11 线程1：[信息] 收到失败数据保存请求: 支付信息错误, 数据: <EMAIL>
2025-08-03 07:58:11 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:58:11 [信息] 多线程状态已重置
2025-08-03 07:58:11 [信息] 线程1请求保存失败数据: 支付信息错误, 数据: <EMAIL>
2025-08-03 07:58:11 [信息] 已处理失败数据: <EMAIL>, 失败原因: 支付信息错误
2025-08-03 07:58:11 [信息] 线程1失败数据已保存: 支付信息错误, 数据: <EMAIL>
2025-08-03 07:58:11 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:58:11 [信息] 多线程状态已重置
2025-08-03 07:58:11 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 支付信息错误 (进度: 58%)
2025-08-03 07:58:11 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 07:58:11 [信息] 多线程状态已重置
2025-08-03 07:58:11 线程1：[信息] [信息] 线程1注册失败，数据已归类，注册流程终止 (进度: 58%)
2025-08-03 07:58:11 线程1：[信息] 已继续
2025-08-03 07:58:11 [信息] 线程1已继续
2025-08-03 07:58:38 [信息] 多线程窗口引用已清理
2025-08-03 07:58:38 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 07:58:38 [信息] 多线程管理窗口正在关闭
2025-08-03 07:58:41 [信息] 程序正在退出，开始清理工作...
2025-08-03 07:58:41 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 07:58:41 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 07:58:41 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 07:58:41 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 07:58:41 [信息] 程序退出清理工作完成
2025-08-03 07:58:45 [信息] AWS自动注册工具启动
2025-08-03 07:58:45 [信息] 程序版本: 1.0.0.0
2025-08-03 07:58:45 [信息] 启动时间: 2025-08-03 07:58:45
2025-08-03 07:58:45 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 07:58:45 [信息] 线程数量已选择: 1
2025-08-03 07:58:45 [信息] 线程数量选择初始化完成
2025-08-03 07:58:45 [信息] 程序初始化完成
2025-08-03 08:01:00 [信息] 线程数量已选择: 2
2025-08-03 08:01:01 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 08:01:03 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 08:01:04 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 08:01:04 [信息] 成功加载 7 条数据
2025-08-03 08:01:08 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 08:01:08 [信息] 开始启动多线程注册，线程数量: 2
2025-08-03 08:01:08 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 7
2025-08-03 08:01:08 [信息] 所有线程已停止并清理
2025-08-03 08:01:08 [信息] 正在初始化多线程服务...
2025-08-03 08:01:08 [信息] 榴莲手机API服务已初始化
2025-08-03 08:01:08 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-03 08:01:08 [信息] 多线程服务初始化完成
2025-08-03 08:01:08 [信息] 数据分配完成：共7条数据分配给2个线程
2025-08-03 08:01:08 [信息] 线程1分配到4条数据
2025-08-03 08:01:08 [信息] 线程2分配到3条数据
2025-08-03 08:01:08 [信息] 屏幕工作区域: 1280x672
2025-08-03 08:01:08 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 08:01:08 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 08:01:08 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 08:01:08 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=14 GB
2025-08-03 08:01:08 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 08:01:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 08:01:08 [信息] 屏幕工作区域: 1280x672
2025-08-03 08:01:08 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 08:01:08 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 08:01:08 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 08:01:08 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=6 GB
2025-08-03 08:01:08 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-03 08:01:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 08:01:08 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 08:01:08 [信息] 多线程注册启动成功，共2个线程
2025-08-03 08:01:08 线程1：[信息] 开始启动注册流程
2025-08-03 08:01:08 线程2：[信息] 开始启动注册流程
2025-08-03 08:01:08 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 08:01:08 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 08:01:08 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 08:01:08 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 08:01:08 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 08:01:08 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 08:01:08 [信息] 多线程管理窗口已初始化
2025-08-03 08:01:08 [信息] UniformGrid列数已更新为: 1
2025-08-03 08:01:08 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 08:01:08 [信息] 多线程管理窗口已打开
2025-08-03 08:01:08 [信息] 多线程注册启动成功，共2个线程
2025-08-03 08:01:09 [信息] UniformGrid列数已更新为: 1
2025-08-03 08:01:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 08:01:09 线程1：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 08:01:09 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 08:01:09 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 08:01:09 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 08:01:09 [信息] UniformGrid列数已更新为: 1
2025-08-03 08:01:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 08:01:09 线程2：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 08:01:09 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 08:01:09 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-03 08:01:09 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 08:01:12 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 08:01:12 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 08:01:14 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 08:01:14 线程2：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 08:01:14 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 16.0544, 108.2022 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=16.0544, 经度=108.2022
2025-08-03 08:01:14 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 08:01:14 线程1：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 08:01:14 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 08:01:14 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 12核 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=14 GB
2025-08-03 08:01:14 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 24核 (进度: 0%)
2025-08-03 08:01:14 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=6 GB
2025-08-03 08:01:15 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 08:01:17 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 08:01:18 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1746x1124
   • 可用区域: 1746x1084

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.51
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 08:01:18 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1918x1010
   • 可用区域: 1918x970

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.40
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 08:01:18 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1746x1124    • 可用区域: 1746x1084   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.51    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 08:01:18 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1918x1010    • 可用区域: 1918x970   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.40    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 08:01:19 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 08:01:19 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 08:01:19 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 08:01:19 线程1：[信息] 浏览器启动成功
2025-08-03 08:01:19 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 08:01:19 线程2：[信息] 浏览器启动成功
2025-08-03 08:01:19 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 08:01:19 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 08:01:19 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 08:01:19 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 08:01:19 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 08:01:19 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 08:01:19 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 08:01:19 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 08:01:19 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 08:01:19 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 08:01:19 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 08:01:19 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 08:01:19 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 08:01:19 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 08:01:19 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 08:01:20 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 08:01:20 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 08:01:20 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 08:01:20 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 08:01:20 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 08:01:20 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 08:01:34 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 08:01:34 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 08:01:34 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 08:01:35 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 08:01:35 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 08:01:35 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 08:01:35 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 08:01:35 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 08:01:35 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 08:01:35 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 08:01:35 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 08:01:35 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 08:01:38 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 08:01:38 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 08:01:38 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 08:01:38 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 08:01:38 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:01:41 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35803 字节 (进度: 100%)
2025-08-03 08:01:41 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35803字节，复杂度符合要求 (进度: 100%)
2025-08-03 08:01:41 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35151 字节 (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35151字节，复杂度符合要求 (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 08:01:42 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"r2drsy"},"taskId":"0919fe3e-6ffd-11f0-bf95-5254008382c7"} (进度: 100%)
2025-08-03 08:01:42 线程1：[信息] [信息] 第一页第1次识别结果: r2drsy → 转换为小写: r2drsy (进度: 100%)
2025-08-03 08:01:42 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 08:01:42 线程1：[信息] [信息] 已填入验证码: r2drsy (进度: 100%)
2025-08-03 08:01:42 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mg66rn"},"taskId":"0942a410-6ffd-11f0-bc85-2e23c0b40dc6"} (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 第一页第1次识别结果: mg66rn → 转换为小写: mg66rn (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 08:01:42 线程2：[信息] [信息] 已填入验证码: mg66rn (进度: 100%)
2025-08-03 08:01:43 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 08:01:44 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 08:01:44 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 08:01:44 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 08:01:44 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 08:01:45 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-03 08:01:45 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 08:01:45 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 08:01:45 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 08:01:45 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 08:01:45 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 08:01:45 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 08:01:45 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 08:01:45 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-03 08:01:45 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 08:01:45 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 08:01:46 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:46 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 08:01:46
2025-08-03 08:01:47 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:47 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 08:01:47
2025-08-03 08:01:49 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:49 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 08:01:49
2025-08-03 08:01:50 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:50 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 08:01:50
2025-08-03 08:01:52 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:53 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 08:01:53
2025-08-03 08:01:53 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:53 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 08:01:53
2025-08-03 08:01:55 [信息] [线程2] 邮箱验证码获取成功: 170349，立即停止重复请求
2025-08-03 08:01:55 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-03 08:01:55 [信息] [线程2] 已清理响应文件
2025-08-03 08:01:55 线程2：[信息] [信息] 验证码获取成功: 170349，正在自动填入... (进度: 25%)
2025-08-03 08:01:55 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 08:01:55 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 08:01:55 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 08:01:55 [信息] 线程2完成第二页事件已处理
2025-08-03 08:01:56 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-03 08:01:56 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 08:01:56 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-03 08:01:56 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-03 08:01:56 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 08:01:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 08:01:56
2025-08-03 08:01:57 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+523313105399","+526741069282"]}
2025-08-03 08:01:57 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-03 08:01:57 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-03 08:01:57 [信息] 线程1分配榴莲手机号码: +523313105399
2025-08-03 08:01:57 [信息] 线程2分配榴莲手机号码: +526741069282
2025-08-03 08:01:57 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-03 08:01:57 [信息] 批量获取2个手机号码成功
2025-08-03 08:01:59 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-03 08:01:59 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-03 08:02:01 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-03 08:02:01 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 08:02:01
2025-08-03 08:02:01 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 08:02:01 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 08:02:01 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-03 08:02:02 [信息] [线程1] 邮箱验证码获取成功: 894456，立即停止重复请求
2025-08-03 08:02:02 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 08:02:02 [信息] [线程1] 已清理响应文件
2025-08-03 08:02:02 线程1：[信息] [信息] 验证码获取成功: 894456，正在自动填入... (进度: 25%)
2025-08-03 08:02:02 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-03 08:02:02 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-03 08:02:02 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-03 08:02:02 [信息] 线程1完成第二页事件已处理
2025-08-03 08:02:02 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-03 08:02:02 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-03 08:02:02 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-03 08:02:05 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-03 08:02:05 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-03 08:02:05 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 08:02:05 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-03 08:02:05 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-03 08:02:05 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-03 08:02:05 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-03 08:02:05 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-03 08:02:08 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-03 08:02:09 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-03 08:02:09 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-03 08:02:11 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-03 08:02:11 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-03 08:02:11 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-03 08:02:29 线程1：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 38%)
2025-08-03 08:02:30 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-03 08:02:30 [信息] 线程2获取已分配的榴莲手机号码: +526741069282
2025-08-03 08:02:30 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526741069282 (进度: 38%)
2025-08-03 08:02:33 线程2：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 38%)
2025-08-03 08:02:33 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-03 08:02:34 线程2：[信息] [信息] 已选择国家: Vietnam (进度: 38%)
2025-08-03 08:02:34 线程2：[信息] [信息] 已成功选择国家: Vietnam (进度: 38%)
2025-08-03 08:02:34 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-03 08:02:35 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 38%)
2025-08-03 08:02:35 线程1：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 38%)
2025-08-03 08:02:37 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-03 08:02:38 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-03 08:02:38 线程2：[信息] [信息] 已自动获取并填入手机号码: +526741069282 (进度: 38%)
2025-08-03 08:02:39 线程2：[信息] [信息] 使用已获取的手机号码: +526741069282（保存本地号码: +526741069282） (进度: 38%)
2025-08-03 08:02:39 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-03 08:02:42 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-03 08:02:43 线程2：[信息] [信息] 正在选择月份: June (进度: 38%)
2025-08-03 08:02:44 线程2：[信息] [信息] 已选择月份（标准选项）: June (进度: 38%)
2025-08-03 08:02:44 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-03 08:02:44 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-03 08:02:45 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-03 08:02:45 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-03 08:02:45 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-03 08:02:50 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-03 08:02:51 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-03 08:02:51 线程2：[信息] [信息] 已清空并重新填写手机号码: +526741069282 (进度: 38%)
2025-08-03 08:02:51 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-03 08:02:53 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-03 08:02:53 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 08:02:53 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 08:02:53 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 08:02:53 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 08:02:55 线程1：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 38%)
2025-08-03 08:02:55 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 38%)
2025-08-03 08:02:55 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 38%)
2025-08-03 08:02:55 线程1：[信息] [信息] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register&refid=em_127222&p=free&c=hp&z=1&redirect_url=https%3A%2F%2Faws.amazon.com%2Fregistration-confirmation#/account (进度: 38%)
2025-08-03 08:02:55 线程1：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 38%)
2025-08-03 08:02:55 线程1：[信息] [信息] 📊 分析结果: 第四页-联系信息(3/4个元素匹配), 第六页-手机验证(1/3个元素匹配) (进度: 48%)
2025-08-03 08:02:55 线程1：[信息] [信息]  智能检测到当前在第6页 (进度: 48%)
2025-08-03 08:02:55 线程1：[信息] [信息] 智能检测到当前在第6页，开始智能处理... (进度: 48%)
2025-08-03 08:02:55 线程1：[信息] [信息] 检测第6页手机验证状态... (进度: 68%)
2025-08-03 08:02:56 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 08:02:56 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:02:57 线程1：[信息] [信息] 所有自动线程已停止 (进度: 68%)
2025-08-03 08:02:57 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 68%)
2025-08-03 08:02:57 线程1：[信息] 已暂停
2025-08-03 08:02:57 [信息] 线程1已暂停
2025-08-03 08:02:57 [信息] 线程1已暂停
2025-08-03 08:02:58 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 6 (进度: 68%)
2025-08-03 08:02:58 线程1：[信息] [信息]  进行智能页面检测... (进度: 68%)
2025-08-03 08:02:58 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 68%)
2025-08-03 08:02:58 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 68%)
2025-08-03 08:02:58 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 68%)
2025-08-03 08:02:58 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-03 08:02:58 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-03 08:02:58 线程1：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-03 08:02:58 [信息] 线程1获取已分配的榴莲手机号码: +523313105399
2025-08-03 08:02:58 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +523313105399 (进度: 100%)
2025-08-03 08:02:58 线程1：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 100%)
2025-08-03 08:02:58 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-03 08:02:59 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34886 字节 (进度: 100%)
2025-08-03 08:02:59 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34886字节，复杂度符合要求 (进度: 100%)
2025-08-03 08:02:59 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 08:03:00 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fhhs64"},"taskId":"378ded02-6ffd-11f0-bf43-7234de99a3d9"} (进度: 100%)
2025-08-03 08:03:00 线程2：[信息] [信息] 第六页第1次识别结果: fhhs64 → 转换为小写: fhhs64 (进度: 100%)
2025-08-03 08:03:00 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 08:03:02 线程1：[信息] [信息] 已选择国家: Vietnam (进度: 100%)
2025-08-03 08:03:02 线程1：[信息] [信息] 已成功选择国家: Vietnam (进度: 100%)
2025-08-03 08:03:02 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 08:03:02 线程2：[信息] [信息] 第六页已填入验证码: fhhs64 (进度: 100%)
2025-08-03 08:03:02 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 08:03:02 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 08:03:04 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-03 08:03:05 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-03 08:03:05 线程1：[信息] [信息] 已自动获取并填入手机号码: +523313105399 (进度: 100%)
2025-08-03 08:03:05 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 08:03:05 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 08:03:06 线程1：[信息] [信息] 使用已获取的手机号码: +523313105399（保存本地号码: +523313105399） (进度: 100%)
2025-08-03 08:03:06 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-03 08:03:08 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 08:03:09 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-03 08:03:10 线程1：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-03 08:03:10 线程1：[信息] [信息] 已选择月份（标准选项）: August (进度: 100%)
2025-08-03 08:03:11 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 08:03:11 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 08:03:11 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 08:03:11 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 08:03:11 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-03 08:03:11 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-03 08:03:12 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-03 08:03:12 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-03 08:03:12 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 08:03:16 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-03 08:03:16 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 08:03:16 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 08:03:16 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 08:03:25 线程1：[信息] [信息] 第6页暂停处理失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Mobile phone number" }) (进度: 100%)
2025-08-03 08:03:25 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-03 08:03:25 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 08:03:26 [错误] [榴莲API] 获取验证码请求超时，尝试 1/4: The request was canceled due to the configured HttpClient.Timeout of 10 seconds elapsing.
2025-08-03 08:03:26 [信息] [榴莲API] 1000ms后重试获取验证码...
2025-08-03 08:03:27 [信息] [榴莲API] 获取验证码，尝试 2/4
2025-08-03 08:03:32 线程2：[信息] [信息] 线程2验证码获取成功: 0509 (进度: 100%)
2025-08-03 08:03:32 [信息] 线程2手机号码已加入释放队列: +526741069282 (原因: 获取验证码成功)
2025-08-03 08:03:32 线程2：[信息] [信息] 线程2验证码获取成功: 0509，立即填入验证码... (进度: 100%)
2025-08-03 08:03:33 线程2：[信息] [信息] 线程2已自动填入手机验证码: 0509 (进度: 100%)
2025-08-03 08:03:34 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-03 08:03:34 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 08:03:38 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-03 08:03:38 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 08:03:38 [信息] 开始释放1个手机号码
2025-08-03 08:03:38 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 08:03:38 [信息] [手机API] 释放手机号码: +526741069282
2025-08-03 08:03:38 [信息] [手机API] 手机号码释放成功: +526741069282
2025-08-03 08:03:38 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-03 08:03:39 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 08:03:39 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 08:03:42 线程1：[信息] [信息] 选择国家代码失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Country or region code" }) (进度: 100%)
2025-08-03 08:03:47 [信息] 获取线程2当前数据: <EMAIL>
2025-08-03 08:03:47 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-03 08:03:47 线程2：[信息] 数据详情: <EMAIL>|L9oNgPV7|Dang Hoang.Anh|Hoa Phat Group|9400 Nguyen Hue Street|Hoi An|DN|530000|4374210040154580|06|27|411|Dang Hoang.Anh|MTU2K6F621n|VN
2025-08-03 08:03:47 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-03 08:03:47 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-03 08:03:47 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-03 08:03:47 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 08:03:47 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-03 08:03:47 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：MTU2K6F621n ③AWS密码：L9oNgPV7 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 08:03:47 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 08:03:47 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250803_080108
2025-08-03 08:03:47 线程2：[信息] 已终止
2025-08-03 08:03:47 [信息] 线程2已终止
2025-08-03 08:03:47 [信息] 开始处理线程2终止数据，共1个数据
2025-08-03 08:03:47 [信息] 处理线程2终止数据: <EMAIL>
2025-08-03 08:03:47 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-03 08:03:47 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-03 08:03:47 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-03 08:03:47 [信息] UniformGrid列数已更新为: 1
2025-08-03 08:03:47 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 08:03:47 [信息] 线程2已终止
2025-08-03 08:03:48 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 08:03:48 线程1：[信息] [信息] 已清空并重新填写手机号码: +523313105399 (进度: 100%)
2025-08-03 08:03:49 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-03 08:03:51 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 08:03:51 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 08:03:51 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 08:03:51 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 08:03:51 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 08:03:54 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 08:03:54 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:04:04 线程2：[信息] [信息] 点击完成注册按钮失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 88%)
2025-08-03 08:04:04 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 88%)
2025-08-03 08:04:07 线程1：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-03 08:04:07 线程1：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-03 08:04:07 线程1：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-03 08:04:07 线程1：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-03 08:04:07 线程1：[信息] 已继续
2025-08-03 08:04:07 [信息] 线程1已继续
2025-08-03 08:04:07 线程2：[信息] [信息] 线程2自动填入手机验证码或点击继续按钮失败: 完成注册流程失败: Target page, context or browser has been closed (进度: 88%)
2025-08-03 08:04:09 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-03 08:04:11 线程1：[信息] [信息] 已清空并重新填写手机号码: +523313105399 (进度: 100%)
2025-08-03 08:04:12 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-03 08:04:14 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 08:04:14 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 08:04:14 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 08:04:14 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 08:04:14 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 08:04:17 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 08:04:17 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:04:20 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35176 字节 (进度: 100%)
2025-08-03 08:04:20 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35176字节，复杂度符合要求 (进度: 100%)
2025-08-03 08:04:20 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 08:04:21 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bz62pm"},"taskId":"67acc530-6ffd-11f0-a1bf-5254008382c7"} (进度: 100%)
2025-08-03 08:04:21 线程1：[信息] [信息] 第六页第1次识别结果: bz62pm → 转换为小写: bz62pm (进度: 100%)
2025-08-03 08:04:21 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 08:04:21 线程1：[信息] [信息] 第六页已填入验证码: bz62pm (进度: 100%)
2025-08-03 08:04:21 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 08:04:26 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 08:04:26 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-03 08:04:28 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 08:04:32 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31136 字节 (进度: 100%)
2025-08-03 08:04:32 线程1：[信息] [信息] ✅ 图片验证通过：200x70px，31136字节，复杂度符合要求 (进度: 100%)
2025-08-03 08:04:32 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 08:04:33 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"cw2ngh"},"taskId":"6ece3506-6ffd-11f0-bf95-5254008382c7"} (进度: 100%)
2025-08-03 08:04:33 线程1：[信息] [信息] 第六页第2次识别结果: cw2ngh → 转换为小写: cw2ngh (进度: 100%)
2025-08-03 08:04:33 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 08:04:33 线程1：[信息] [信息] 第六页已填入验证码: cw2ngh (进度: 100%)
2025-08-03 08:04:33 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 08:04:36 线程1：[信息] [信息] 第2次图形验证码识别成功 (进度: 100%)
2025-08-03 08:04:36 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 08:04:39 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 08:04:42 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 08:04:42 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 08:04:42 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 08:04:42 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 08:04:47 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 08:04:47 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 08:04:47 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 08:04:47 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 08:04:49 线程1：[信息] [信息] 线程1验证码获取成功: 3438 (进度: 100%)
2025-08-03 08:04:49 [信息] 线程1手机号码已加入释放队列: +523313105399 (原因: 获取验证码成功)
2025-08-03 08:04:49 线程1：[信息] [信息] 线程1验证码获取成功: 3438，立即填入验证码... (进度: 100%)
2025-08-03 08:04:49 线程1：[信息] [信息] 线程1已自动填入手机验证码: 3438 (进度: 100%)
2025-08-03 08:04:50 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-03 08:04:50 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 08:04:53 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-03 08:04:53 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-03 08:04:53 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-03 08:04:54 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-03 08:04:57 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-03 08:04:57 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-03 08:05:03 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-03 08:05:03 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-03 08:05:03 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-03 08:05:08 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 08:05:08 [信息] 开始释放1个手机号码
2025-08-03 08:05:08 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 08:05:08 [信息] [手机API] 释放手机号码: +523313105399
2025-08-03 08:05:08 [信息] [手机API] 手机号码释放成功: +523313105399
2025-08-03 08:05:09 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 08:05:09 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 08:05:09 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-03 08:05:09 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-03 08:05:10 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-03 08:05:10 [信息] 成功点击更多按钮
2025-08-03 08:05:13 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-03 08:05:13 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-03 08:05:13 [信息] 成功点击账户信息按钮
2025-08-03 08:05:14 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-03 08:05:14 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-03 08:05:14 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-03 08:05:14 [信息] 成功定位到'安全凭证'链接
2025-08-03 08:05:20 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-03 08:05:20 [信息] 成功点击'安全凭证'链接
2025-08-03 08:05:20 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-03 08:05:23 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-03 08:05:23 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-03 08:05:23 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-03 08:05:23 [信息] 检测到账单问题，开始处理
2025-08-03 08:05:23 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-03 08:05:23 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 08:05:23 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 08:05:23 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 08:05:23 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 08:05:23 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：NfZu5v6560 ③AWS密码：6u4bQS9Z ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-03 08:05:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 08:05:23 [信息] 多线程状态已重置
2025-08-03 08:05:23 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-03 08:05:23 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-03 08:05:23 [信息] 注册完成 - 账单提示处理
2025-08-03 08:05:23 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-03 08:05:23 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-03 08:05:23 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-03 08:05:23 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-03 08:05:23 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-03 08:05:23 线程1：[信息] 已继续
2025-08-03 08:05:23 [信息] 线程1已继续
2025-08-03 08:05:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 08:05:23 [信息] 多线程状态已重置
2025-08-03 08:05:23 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-03 08:05:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 08:05:23 [信息] 多线程状态已重置
2025-08-03 08:05:23 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-03 08:05:28 [信息] 多线程窗口引用已清理
2025-08-03 08:05:28 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 08:05:28 [信息] 多线程管理窗口正在关闭
2025-08-03 08:05:29 [信息] 程序正在退出，开始清理工作...
2025-08-03 08:05:29 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 08:05:29 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 08:05:29 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 08:05:29 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 08:05:29 [信息] 程序退出清理工作完成
2025-08-03 09:31:25 [信息] AWS自动注册工具启动
2025-08-03 09:31:25 [信息] 程序版本: 1.0.0.0
2025-08-03 09:31:25 [信息] 启动时间: 2025-08-03 09:31:25
2025-08-03 09:31:25 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 09:31:25 [信息] 线程数量已选择: 1
2025-08-03 09:31:25 [信息] 线程数量选择初始化完成
2025-08-03 09:31:25 [信息] 程序初始化完成
2025-08-03 09:31:49 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 09:31:51 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-以色列.txt
2025-08-03 09:31:52 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-以色列.txt
2025-08-03 09:31:52 [信息] 成功加载 1 条数据
2025-08-03 09:31:52 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-以色列.txt
2025-08-03 09:31:52 [信息] 成功加载 1 条数据
2025-08-03 09:31:55 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 09:31:55 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-03 09:31:57 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-03 09:31:57 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-03 09:31:57 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-03 09:31:57 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-03 09:31:57 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-03 09:31:58 [系统状态] 创建无痕模式上下文...
2025-08-03 09:32:00 [系统状态] 使用默认时区: America/New_York
2025-08-03 09:32:00 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-03 09:32:00 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 10核
2025-08-03 09:32:00 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=12 GB
2025-08-03 09:32:01 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-03 09:32:01 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 10
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5E6F7A8B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_012
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-U6V7W8X
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1774x1064
   • 可用区域: 1774x1024

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.60
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================
2025-08-03 09:32:01 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 10    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5E6F7A8B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_012    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-U6V7W8X    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1774x1064    • 可用区域: 1774x1024   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.60    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 09:32:01 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-03 09:32:01 [注册开始] 邮箱: <EMAIL>, 索引: 1/1
2025-08-03 09:32:01 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 09:32:01 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-03 09:32:01 [系统状态] 在现有窗口中新建标签页...
2025-08-03 09:32:01 [系统状态] 使用现有的浏览器上下文
2025-08-03 09:32:01 [系统状态] 正在新建标签页...
2025-08-03 09:32:02 [系统状态] 已设置页面视口大小为600x400
2025-08-03 09:32:02 [系统状态] 验证无痕Chrome模式状态...
2025-08-03 09:32:02 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-03 09:32:02 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-03 09:32:02 [系统状态] 正在打开AWS注册页面...
2025-08-03 09:32:18 [系统状态] 正在执行第一页注册...
2025-08-03 09:32:18 [系统状态] 🔍 等待第一页加载完成...
2025-08-03 09:32:19 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-03 09:32:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 09:32:19 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-03 09:32:22 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-03 09:32:22 [系统状态] ✅ 未检测到IP异常错误，继续流程
2025-08-03 09:32:22 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-03 09:32:22 [系统状态] 🔍 第1次检测图形验证码...
2025-08-03 09:32:22 [系统状态] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 09:32:22 [系统状态] ✅ 第1次检测发现图形验证码！
2025-08-03 09:32:22 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-08-03 09:32:22 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-08-03 09:32:22 [系统状态] 第一页图形验证码自动识别模式
2025-08-03 09:32:22 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-08-03 09:32:28 [系统状态] 第一页已从iframe截取验证码图片，大小: 35342 字节
2025-08-03 09:32:28 [系统状态] ✅ 图片验证通过：201x71px，35342字节，复杂度符合要求
2025-08-03 09:32:28 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 09:32:30 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"sy3mdh"},"taskId":"b7a0bfa4-7009-11f0-bf95-5254008382c7"}
2025-08-03 09:32:30 [系统状态] 第一页第1次识别结果: sy3mdh → 转换为小写: sy3mdh
2025-08-03 09:32:30 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-08-03 09:32:30 [系统状态] 已填入验证码: sy3mdh
2025-08-03 09:32:30 [系统状态] 已点击iframe内Submit按钮
2025-08-03 09:32:32 [系统状态] 第一页第1次图形验证码识别成功
2025-08-03 09:32:32 [系统状态] 第一页图形验证码自动完成
2025-08-03 09:32:32 [系统状态] 📋 第一页图形验证码检查完成
2025-08-03 09:32:32 [系统状态] 第一页完成，等待验证码页面...
2025-08-03 09:32:32 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 09:32:32 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 09:32:32 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 09:32:58 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 09:32:58 [系统状态] 所有自动线程已停止
2025-08-03 09:32:58 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 09:33:00 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 09:33:00 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 09:33:00 [系统状态]  进行智能页面检测...
2025-08-03 09:33:00 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 09:33:00 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 09:33:00 [系统状态] ⚠️ 未找到匹配的页面按钮或链接
2025-08-03 09:33:00 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-08-03 09:33:00 [系统状态] 🔬 执行详细页面分析...
2025-08-03 09:33:00 [系统状态] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register
2025-08-03 09:33:00 [系统状态] 📋 页面标题: AWS Console - Signup
2025-08-03 09:33:01 [系统状态] 📊 分析结果: 第三页-密码设置(2/3个元素匹配)
2025-08-03 09:33:01 [系统状态]  智能检测到当前在第3页
2025-08-03 09:33:01 [系统状态] 智能检测到当前在第3页，开始智能处理...
2025-08-03 09:33:06 [系统状态] 等待密码设置页面加载...
2025-08-03 09:33:06 [系统状态] 开始填写密码信息...
2025-08-03 09:33:06 [系统状态] 第一个密码输入框已清空并重新填写完成
2025-08-03 09:33:06 [系统状态] 确认密码输入框已清空并重新填写完成
2025-08-03 09:33:06 [系统状态] 密码填写完成，点击继续按钮...
2025-08-03 09:33:07 [系统状态] 密码设置完成，等待页面跳转...
2025-08-03 09:33:10 [系统状态] 第三页完成，进入第3.5页（账户类型确认页面）...
2025-08-03 09:33:10 [系统状态] 等待账户类型确认页面加载...
2025-08-03 09:33:10 [系统状态] 开始处理账户类型确认...
2025-08-03 09:33:14 [系统状态] 已点击Choose paid plan按钮，账户类型确认完成
2025-08-03 09:33:14 [系统状态] 账户类型确认完成，进入联系信息页面...
2025-08-03 09:33:34 [系统状态] 第3.5页完成，页面已跳转到第4页
2025-08-03 09:33:34 [系统状态] 开始后台获取手机号码，同时填写其他信息...
2025-08-03 09:33:34 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-03 09:33:34 [系统状态] 数据国家代码为IL，需要选择Israel
2025-08-03 09:33:35 [系统状态] 已点击国家/地区选择器，正在展开列表...
2025-08-03 09:33:35 [系统状态] 后台获取榴莲手机号码成功: +528713311983，已保存到注册数据
2025-08-03 09:33:36 [系统状态] 已选择国家: Israel
2025-08-03 09:33:36 [系统状态] 已成功选择国家: Israel
2025-08-03 09:33:36 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-03 09:33:37 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-03 09:33:40 [系统状态] 已选择国家代码 +52
2025-08-03 09:33:41 [系统状态] 等待后台获取的手机号码结果...
2025-08-03 09:33:41 [系统状态] 已自动获取并填入手机号码: +528713311983
2025-08-03 09:33:42 [系统状态] 使用已获取的手机号码: +528713311983（保存本地号码: 8713311983）
2025-08-03 09:33:42 [系统状态] 联系信息完成，等待页面加载...
2025-08-03 09:33:45 [系统状态] 进入付款信息页面...
2025-08-03 09:33:46 [系统状态] 正在选择月份: February
2025-08-03 09:33:46 [系统状态] 已选择月份（标准选项）: February
2025-08-03 09:33:47 [系统状态] 正在选择年份: 2028
2025-08-03 09:33:48 [系统状态] 已选择年份（标准选项）: 2028
2025-08-03 09:33:48 [系统状态] 付款信息完成，进入验证码验证页面...
2025-08-03 09:33:48 [系统状态] 开始填写验证码验证页面...
2025-08-03 09:33:48 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-03 09:33:54 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-03 09:33:55 [系统状态] 已选择国家代码: +52
2025-08-03 09:33:55 [系统状态] 已清空并重新填写手机号码: 8713311983
2025-08-03 09:33:55 [系统状态] 已点击发送验证码按钮
2025-08-03 09:33:57 [系统状态] 🔍 检查是否出现验证手机区号错误...
2025-08-03 09:33:57 [系统状态] ✅ 未检测到验证手机区号错误，继续执行
2025-08-03 09:33:57 [系统状态] 手机号码自动模式 + 图形验证码自动模式：开始自动处理...
2025-08-03 09:33:57 [系统状态] 自动模式：开始处理图形验证码...
2025-08-03 09:33:57 [系统状态] 第六页点击发送验证码后，等待图形验证码出现...
2025-08-03 09:34:00 [系统状态] 第六页图形验证码自动识别模式，开始处理...
2025-08-03 09:34:00 [系统状态] 第六页第1次尝试自动识别图形验证码...
2025-08-03 09:34:04 [系统状态] 第六页已从iframe截取验证码图片，大小: 35799 字节
2025-08-03 09:34:04 [系统状态] ✅ 图片验证通过：201x71px，35799字节，复杂度符合要求
2025-08-03 09:34:04 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 09:34:06 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"42z2t4"},"taskId":"f0f0234e-7009-11f0-bf95-5254008382c7"}
2025-08-03 09:34:06 [系统状态] 第六页第1次识别结果: 42z2t4 → 转换为小写: 42z2t4
2025-08-03 09:34:06 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-03 09:34:06 [系统状态] 第六页已填入验证码: 42z2t4
2025-08-03 09:34:06 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-03 09:34:09 [系统状态] 第1次图形验证码识别成功
2025-08-03 09:34:09 [系统状态] 第六页图形验证码自动完成，检查验证结果...
2025-08-03 09:34:12 [系统状态] 第六页图形验证码验证成功，进入第七页
2025-08-03 09:34:15 [系统状态] 开始处理第七页 - Continue (step 4 of 5) 页面
2025-08-03 09:34:15 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-03 09:34:15 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-03 09:34:15 [系统状态] 第七页自动模式：等待5秒后开始获取验证码...
2025-08-03 09:34:20 [系统状态] 开始自动获取验证码，2分钟超时...
2025-08-03 09:34:20 [系统状态] 第1次尝试获取验证码...（剩余4次尝试）
2025-08-03 09:34:20 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 09:34:21 [系统状态] 成功获取验证码: 0654，立即填入验证码...
2025-08-03 09:34:21 [系统状态] 已自动填入手机验证码: 0654
2025-08-03 09:34:22 [系统状态] 正在自动点击Continue按钮...
2025-08-03 09:34:23 [系统状态] 手机验证码验证完成，继续执行后续步骤...
2025-08-03 09:34:23 [系统状态] 榴莲手机号码已加入黑名单
2025-08-03 09:34:26 [系统状态] 验证码验证完成，等待页面加载...
2025-08-03 09:34:26 [系统状态] 已点击完成注册按钮，正在处理...
2025-08-03 09:34:26 [系统状态] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单失败: 请勿重复操作
2025-08-03 09:34:27 [系统状态] 等待完成注册页面加载...
2025-08-03 09:34:30 [系统状态] 完成注册页面加载完成
2025-08-03 09:34:30 [系统状态] 🔍 正在查找'Go to the AWS Management Console'链接...
2025-08-03 09:34:34 [系统状态] 🖱️ 已点击跳转管理控制台链接
2025-08-03 09:34:34 [系统状态] 🔗 通过导航进入IAM安全凭证页面...
2025-08-03 09:34:34 [系统状态] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）...
2025-08-03 09:34:35 [系统状态] 邮箱验证码自动获取失败: 获取验证码超时（2分钟内未获取到有效验证码）
2025-08-03 09:34:35 [系统状态] 🔴 Microsoft获取验证码失败，转为手动模式
2025-08-03 09:34:42 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 09:34:42 [系统状态]  继续注册被调用，当前状态: WaitingForVerification，当前步骤: 7
2025-08-03 09:34:42 [系统状态]  进行智能页面检测...
2025-08-03 09:34:42 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 09:34:42 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 09:34:42 [系统状态] ⚠️ 未找到匹配的页面按钮或链接
2025-08-03 09:34:42 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-08-03 09:34:42 [系统状态] 🔬 执行详细页面分析...
2025-08-03 09:34:42 [系统状态] 📄 页面URL: https://ap-southeast-2.console.aws.amazon.com/console/home?region=ap-southeast-2#
2025-08-03 09:34:42 [系统状态] 📋 页面标题: AWS Management Console
2025-08-03 09:34:42 [系统状态] 📊 分析结果: 未识别的页面
2025-08-03 09:34:42 [系统状态] ❌ 详细分析也无法识别页面
2025-08-03 09:34:42 [系统状态] 当前状态: WaitingForVerification, 步骤: 7，尝试智能检测页面...
2025-08-03 09:34:42 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 09:34:42 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 09:34:42 [系统状态] ⚠️ 未找到匹配的页面按钮或链接
2025-08-03 09:34:42 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-08-03 09:34:42 [系统状态] 🔬 执行详细页面分析...
2025-08-03 09:34:42 [系统状态] 📄 页面URL: https://ap-southeast-2.console.aws.amazon.com/console/home?region=ap-southeast-2#
2025-08-03 09:34:42 [系统状态] 📋 页面标题: AWS Management Console
2025-08-03 09:34:42 [系统状态] 📊 分析结果: 未识别的页面
2025-08-03 09:34:42 [系统状态] ❌ 详细分析也无法识别页面
2025-08-03 09:34:42 [系统状态] 无法检测到当前页面，请手动操作
2025-08-03 09:34:54 [系统状态] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程...
2025-08-03 09:34:54 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-03 09:34:54 [系统状态] 🔍 智能检测更多按钮...
2025-08-03 09:34:54 [系统状态] 🔍 第1次检查更多按钮...
2025-08-03 09:34:55 [系统状态] ⚠️ 第1次检查未找到更多按钮
2025-08-03 09:34:55 [信息] 第1次检查未找到更多按钮
2025-08-03 09:34:55 [系统状态] ⏳ 等待3秒后重新检查...
2025-08-03 09:34:58 [系统状态] 🔍 第2次检查更多按钮...
2025-08-03 09:34:59 [系统状态] ⚠️ 第2次检查未找到更多按钮
2025-08-03 09:34:59 [信息] 第2次检查未找到更多按钮
2025-08-03 09:34:59 [系统状态] ⏳ 等待3秒后重新检查...
2025-08-03 09:35:02 [系统状态] 🔍 第3次检查更多按钮...
2025-08-03 09:35:03 [系统状态] ⚠️ 第3次检查未找到更多按钮
2025-08-03 09:35:03 [信息] 第3次检查未找到更多按钮
2025-08-03 09:35:03 [系统状态] ❌ 3次检查均未找到更多按钮
2025-08-03 09:35:03 [信息] 3次检查均未找到更多按钮，控制台按钮处理失败
2025-08-03 09:35:03 [系统状态] ⚠️ 进入密钥提取页面超时，请等待页面加载完成后点击继续注册
2025-08-03 09:35:03 [信息] 进入密钥提取页面超时，暂停注册等待用户手动继续
2025-08-03 09:35:44 [按钮操作] 终止注册 -> 终止当前注册流程
2025-08-03 09:35:44 [系统状态] 所有自动线程已停止
2025-08-03 09:35:44 [信息] 检测到所有数据处理完成，已完全重置按钮状态
2025-08-03 09:35:44 [系统状态] 注册已终止
2025-08-03 09:35:44 [注册结束] 注册被终止 - 邮箱: <EMAIL>
2025-08-03 09:35:44 [系统状态] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 09:35:44 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 09:35:44 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：UGC53R815 ③AWS密码：G3YF9ozO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 09:35:44 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 09:36:10 [信息] 程序正在退出，开始清理工作...
2025-08-03 09:36:10 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 09:36:10 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 09:36:10 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 09:36:10 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 09:36:10 [信息] 程序退出清理工作完成
2025-08-03 09:46:06 [信息] AWS自动注册工具启动
2025-08-03 09:46:06 [信息] 程序版本: 1.0.0.0
2025-08-03 09:46:06 [信息] 启动时间: 2025-08-03 09:46:06
2025-08-03 09:46:06 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 09:46:06 [信息] 线程数量已选择: 1
2025-08-03 09:46:07 [信息] 线程数量选择初始化完成
2025-08-03 09:46:07 [信息] 程序初始化完成
2025-08-03 09:46:08 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 09:46:11 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 09:46:12 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 09:46:12 [信息] 成功加载 5 条数据
2025-08-03 09:46:17 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 09:46:17 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-03 09:46:17 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-03 09:46:17 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-03 09:46:17 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-03 09:46:17 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 09:46:17 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-03 09:46:19 [系统状态] 创建无痕模式上下文...
2025-08-03 09:46:21 [系统状态] 使用默认时区: America/New_York
2025-08-03 09:46:21 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-03 09:46:21 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 6核
2025-08-03 09:46:21 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=64 GB
2025-08-03 09:46:22 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-03 09:46:22 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 6
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: null

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1899x1152
   • 可用区域: 1899x1112

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.51
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================
2025-08-03 09:46:22 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 6    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: null   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1899x1152    • 可用区域: 1899x1112   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.51    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 09:46:22 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-03 09:46:22 [注册开始] 邮箱: <EMAIL>, 索引: 1/5
2025-08-03 09:46:22 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 09:46:22 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-03 09:46:22 [系统状态] 在现有窗口中新建标签页...
2025-08-03 09:46:22 [系统状态] 使用现有的浏览器上下文
2025-08-03 09:46:22 [系统状态] 正在新建标签页...
2025-08-03 09:46:23 [系统状态] 已设置页面视口大小为600x400
2025-08-03 09:46:23 [系统状态] 验证无痕Chrome模式状态...
2025-08-03 09:46:23 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-03 09:46:23 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-03 09:46:23 [系统状态] 正在打开AWS注册页面...
2025-08-03 09:46:48 [系统状态] 正在执行第一页注册...
2025-08-03 09:46:48 [系统状态] 🔍 等待第一页加载完成...
2025-08-03 09:46:48 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-03 09:46:48 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 09:46:49 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-03 09:46:52 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-03 09:46:52 [系统状态] ⚠️ 检测到错误信息，开始重试机制...
2025-08-03 09:46:52 [信息] 检测到错误信息，开始重试机制
2025-08-03 09:46:52 [系统状态] 🔄 第1次重试点击验证邮箱按钮...
2025-08-03 09:46:52 [信息] 第1次重试点击验证邮箱按钮
2025-08-03 09:46:55 [系统状态] ✅ 第1次重试：已点击验证邮箱按钮
2025-08-03 09:46:55 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-03 09:46:59 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 09:46:59 [系统状态] 所有自动线程已停止
2025-08-03 09:46:59 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 09:47:00 [系统状态] ✅ 第1次重试成功：已到达第二页，继续正常流程
2025-08-03 09:47:00 [信息] 第1次重试成功：已到达第二页
2025-08-03 09:47:00 [系统状态] IP异常检测后暂停，停止执行后续流程
2025-08-03 09:47:21 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 09:47:21 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 1
2025-08-03 09:47:21 [系统状态]  进行智能页面检测...
2025-08-03 09:47:21 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 09:47:21 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 09:47:21 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 09:47:21 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 09:47:21 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 09:47:21 [系统状态]  智能检测到当前在第2页
2025-08-03 09:47:21 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 09:47:21 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 09:47:21 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 09:47:21 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 09:47:21 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 09:47:21 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 09:47:40 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 09:47:40 [系统状态] 所有自动线程已停止
2025-08-03 09:47:40 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 09:48:52 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 09:48:52 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 09:48:52 [系统状态]  进行智能页面检测...
2025-08-03 09:48:52 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 09:48:52 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 09:48:52 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 09:48:52 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 09:48:52 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 09:48:52 [系统状态]  智能检测到当前在第2页
2025-08-03 09:48:52 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 09:48:52 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 09:48:52 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 09:48:53 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 09:48:53 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 09:48:53 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 09:49:24 [系统状态] 邮箱验证码自动获取失败: 获取验证码超时（2分钟内未获取到有效验证码）
2025-08-03 09:49:24 [系统状态] 🔴 Microsoft获取验证码失败，转为手动模式
2025-08-03 09:50:09 [信息] 程序正在退出，开始清理工作...
2025-08-03 09:50:09 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 09:50:09 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 09:50:09 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 09:50:09 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 09:50:09 [信息] 程序退出清理工作完成
2025-08-03 10:03:03 [信息] AWS自动注册工具启动
2025-08-03 10:03:03 [信息] 程序版本: 1.0.0.0
2025-08-03 10:03:04 [信息] 启动时间: 2025-08-03 10:03:03
2025-08-03 10:03:04 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 10:03:04 [信息] 线程数量已选择: 1
2025-08-03 10:03:04 [信息] 线程数量选择初始化完成
2025-08-03 10:03:04 [信息] 程序初始化完成
2025-08-03 10:03:05 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 10:03:09 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:03:10 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:03:10 [信息] 成功加载 5 条数据
2025-08-03 10:03:13 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 10:03:13 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-03 10:03:15 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-03 10:03:15 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-03 10:03:15 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-03 10:03:15 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 10:03:15 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-03 10:03:16 [系统状态] 创建无痕模式上下文...
2025-08-03 10:03:18 [系统状态] 使用默认时区: America/New_York
2025-08-03 10:03:18 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-03 10:03:18 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 12核
2025-08-03 10:03:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=4 GB
2025-08-03 10:03:18 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-03 10:03:19 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: null

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1956x1141
   • 可用区域: 1956x1101

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.47
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-03 10:03:19 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: null   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1956x1141    • 可用区域: 1956x1101   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.47    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:03:19 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-03 10:03:19 [注册开始] 邮箱: <EMAIL>, 索引: 1/5
2025-08-03 10:03:19 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 10:03:19 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-03 10:03:19 [系统状态] 在现有窗口中新建标签页...
2025-08-03 10:03:19 [系统状态] 使用现有的浏览器上下文
2025-08-03 10:03:19 [系统状态] 正在新建标签页...
2025-08-03 10:03:19 [系统状态] 已设置页面视口大小为600x400
2025-08-03 10:03:19 [系统状态] 验证无痕Chrome模式状态...
2025-08-03 10:03:19 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-03 10:03:19 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-03 10:03:19 [系统状态] 正在打开AWS注册页面...
2025-08-03 10:03:34 [系统状态] 正在执行第一页注册...
2025-08-03 10:03:34 [系统状态] 🔍 等待第一页加载完成...
2025-08-03 10:03:35 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:03:35 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:03:35 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-03 10:03:38 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-03 10:03:38 [系统状态] ✅ 未检测到IP异常错误，继续流程
2025-08-03 10:03:38 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-03 10:03:38 [系统状态] 🔍 第1次检测图形验证码...
2025-08-03 10:03:38 [系统状态] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 10:03:38 [系统状态] ✅ 第1次检测发现图形验证码！
2025-08-03 10:03:38 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-08-03 10:03:38 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-08-03 10:03:38 [系统状态] 第一页图形验证码自动识别模式
2025-08-03 10:03:38 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-08-03 10:03:42 [系统状态] 第一页已从iframe截取验证码图片，大小: 35412 字节
2025-08-03 10:03:42 [系统状态] ✅ 图片验证通过：201x71px，35412字节，复杂度符合要求
2025-08-03 10:03:42 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 10:03:44 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"76cgys"},"taskId":"14da4380-700e-11f0-8c61-861c57ef4db4"}
2025-08-03 10:03:44 [系统状态] 第一页第1次识别结果: 76cgys → 转换为小写: 76cgys
2025-08-03 10:03:44 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-08-03 10:03:44 [系统状态] 已填入验证码: 76cgys
2025-08-03 10:03:44 [系统状态] 已点击iframe内Submit按钮
2025-08-03 10:03:46 [系统状态] 第一页第1次图形验证码识别成功
2025-08-03 10:03:46 [系统状态] 第一页图形验证码自动完成
2025-08-03 10:03:46 [系统状态] 📋 第一页图形验证码检查完成
2025-08-03 10:03:46 [系统状态] 第一页完成，等待验证码页面...
2025-08-03 10:03:46 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:03:46 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:03:46 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:04:21 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:04:21 [系统状态] 所有自动线程已停止
2025-08-03 10:04:21 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:05:49 [系统状态] 检测到注册已暂停或终止，停止邮箱验证码处理
2025-08-03 10:06:49 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:06:49 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:06:49 [系统状态]  进行智能页面检测...
2025-08-03 10:06:49 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:06:49 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:06:49 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:06:49 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:06:49 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:06:49 [系统状态]  智能检测到当前在第2页
2025-08-03 10:06:49 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:06:49 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:06:49 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:06:49 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:06:49 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:06:49 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:07:01 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:07:01 [系统状态] 所有自动线程已停止
2025-08-03 10:07:01 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:07:02 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:07:02 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:07:02 [系统状态]  进行智能页面检测...
2025-08-03 10:07:02 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:07:02 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:07:02 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:07:02 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:07:02 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:07:02 [系统状态]  智能检测到当前在第2页
2025-08-03 10:07:02 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:07:02 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:07:02 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:07:02 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:07:02 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:07:02 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:07:24 [信息] 程序正在退出，开始清理工作...
2025-08-03 10:07:24 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 10:07:24 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 10:07:24 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 10:07:24 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 10:07:24 [信息] 程序退出清理工作完成
2025-08-03 10:07:29 [信息] AWS自动注册工具启动
2025-08-03 10:07:29 [信息] 程序版本: 1.0.0.0
2025-08-03 10:07:29 [信息] 启动时间: 2025-08-03 10:07:29
2025-08-03 10:07:29 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 10:07:29 [信息] 线程数量已选择: 1
2025-08-03 10:07:30 [信息] 线程数量选择初始化完成
2025-08-03 10:07:30 [信息] 程序初始化完成
2025-08-03 10:07:31 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 10:07:34 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:07:34 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:07:35 [信息] 成功加载 5 条数据
2025-08-03 10:07:36 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 10:07:36 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-03 10:07:37 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-03 10:07:37 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-03 10:07:37 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-03 10:07:37 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 10:07:37 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-03 10:07:38 [系统状态] 创建无痕模式上下文...
2025-08-03 10:07:40 [系统状态] 使用默认时区: America/New_York
2025-08-03 10:07:40 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-03 10:07:40 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 8核
2025-08-03 10:07:40 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=12 GB
2025-08-03 10:07:41 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-03 10:07:43 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1832x1032
   • 可用区域: 1832x992

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.95
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================
2025-08-03 10:07:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1832x1032    • 可用区域: 1832x992   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.95    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-03 10:07:43 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-03 10:07:43 [注册开始] 邮箱: <EMAIL>, 索引: 1/5
2025-08-03 10:07:43 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 10:07:43 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-03 10:07:43 [系统状态] 在现有窗口中新建标签页...
2025-08-03 10:07:43 [系统状态] 使用现有的浏览器上下文
2025-08-03 10:07:43 [系统状态] 正在新建标签页...
2025-08-03 10:07:43 [系统状态] 已设置页面视口大小为600x400
2025-08-03 10:07:43 [系统状态] 验证无痕Chrome模式状态...
2025-08-03 10:07:43 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-03 10:07:43 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-03 10:07:43 [系统状态] 正在打开AWS注册页面...
2025-08-03 10:08:05 [系统状态] 正在执行第一页注册...
2025-08-03 10:08:05 [系统状态] 🔍 等待第一页加载完成...
2025-08-03 10:08:05 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:08:05 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:08:05 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-03 10:08:08 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-03 10:08:09 [系统状态] ⚠️ 检测到错误信息，开始重试机制...
2025-08-03 10:08:09 [信息] 检测到错误信息，开始重试机制
2025-08-03 10:08:09 [系统状态] 🔄 第1次重试点击验证邮箱按钮...
2025-08-03 10:08:09 [信息] 第1次重试点击验证邮箱按钮
2025-08-03 10:08:11 [系统状态] ✅ 第1次重试：已点击验证邮箱按钮
2025-08-03 10:08:11 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-03 10:08:13 [系统状态] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-03 10:08:13 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-03 10:08:13 [系统状态] ✅ 第1次重试成功：已到达第二页，继续正常流程
2025-08-03 10:08:13 [信息] 第1次重试成功：已到达第二页
2025-08-03 10:08:13 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-03 10:08:13 [系统状态] 🔍 第1次检测图形验证码...
2025-08-03 10:08:13 [系统状态] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 10:08:13 [系统状态] ⏳ 第1次未发现验证码，等待2秒后继续检测...
2025-08-03 10:08:15 [系统状态] 🔍 第2次检测图形验证码...
2025-08-03 10:08:15 [系统状态] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 10:08:15 [系统状态] ✅ 第2次检测发现图形验证码！
2025-08-03 10:08:15 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-08-03 10:08:15 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-08-03 10:08:15 [系统状态] 第一页图形验证码自动识别模式
2025-08-03 10:08:15 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-08-03 10:08:24 [系统状态] 第一页已从iframe截取验证码图片，大小: 35343 字节
2025-08-03 10:08:24 [系统状态] ✅ 图片验证通过：201x71px，35343字节，复杂度符合要求
2025-08-03 10:08:24 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 10:08:27 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bp5mx7"},"taskId":"bd75e558-700e-11f0-bf43-7234de99a3d9"}
2025-08-03 10:08:27 [系统状态] 第一页第1次识别结果: bp5mx7 → 转换为小写: bp5mx7
2025-08-03 10:08:27 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-08-03 10:08:27 [系统状态] 已填入验证码: bp5mx7
2025-08-03 10:08:27 [系统状态] 已点击iframe内Submit按钮
2025-08-03 10:08:29 [系统状态] 第一页第1次图形验证码识别成功
2025-08-03 10:08:29 [系统状态] 第一页图形验证码自动完成
2025-08-03 10:08:29 [系统状态] 📋 第一页图形验证码检查完成
2025-08-03 10:08:29 [系统状态] 第一页完成，等待验证码页面...
2025-08-03 10:08:29 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:08:29 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:08:29 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:08:57 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:08:57 [系统状态] 所有自动线程已停止
2025-08-03 10:08:57 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:10:32 [系统状态] 检测到注册已暂停或终止，停止邮箱验证码处理
2025-08-03 10:10:48 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:10:48 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:10:48 [系统状态]  进行智能页面检测...
2025-08-03 10:10:48 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:10:48 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:10:48 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:10:48 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:10:48 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:10:48 [系统状态]  智能检测到当前在第2页
2025-08-03 10:10:48 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:10:48 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:10:48 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:10:48 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:10:48 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:10:48 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:10:50 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:10:50 [系统状态] 所有自动线程已停止
2025-08-03 10:10:50 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:12:51 [系统状态] 检测到注册已暂停或终止，停止邮箱验证码处理
2025-08-03 10:14:49 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:14:49 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:14:49 [系统状态]  进行智能页面检测...
2025-08-03 10:14:49 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:14:49 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:14:49 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:14:49 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:14:49 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:14:49 [系统状态]  智能检测到当前在第2页
2025-08-03 10:14:49 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:14:49 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:14:49 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:14:49 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:14:49 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:14:49 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:16:52 [系统状态] 邮箱验证码自动获取失败: 获取验证码超时（2分钟内未获取到有效验证码）
2025-08-03 10:16:52 [系统状态] 🔴 Microsoft获取验证码失败，转为手动模式
2025-08-03 10:16:54 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:16:54 [系统状态] 所有自动线程已停止
2025-08-03 10:16:54 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:21:34 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:21:34 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:21:34 [系统状态]  进行智能页面检测...
2025-08-03 10:21:34 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:21:34 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:21:34 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:21:34 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:21:34 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:21:34 [系统状态]  智能检测到当前在第2页
2025-08-03 10:21:34 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:21:34 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:21:34 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:21:34 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:21:34 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:21:34 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:23:06 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:23:06 [系统状态] 所有自动线程已停止
2025-08-03 10:23:06 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:23:36 [系统状态] 检测到注册已暂停或终止，停止邮箱验证码处理
2025-08-03 10:26:20 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:26:20 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 2
2025-08-03 10:26:20 [系统状态]  进行智能页面检测...
2025-08-03 10:26:20 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:26:20 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:26:20 [系统状态] 🎯 找到匹配按钮: 'Verify' → 第2页
2025-08-03 10:26:20 [系统状态] 🔍 疑似第2页，进行二次确认...
2025-08-03 10:26:20 [系统状态] ✅ 确认为第2页：找到Verification code输入框
2025-08-03 10:26:20 [系统状态]  智能检测到当前在第2页
2025-08-03 10:26:20 [系统状态] 智能检测到当前在第2页，开始智能处理...
2025-08-03 10:26:20 [系统状态] 智能检测到第2页，检查邮箱验证码模式...
2025-08-03 10:26:20 [系统状态] 邮箱验证码自动模式，继续自动执行...
2025-08-03 10:26:20 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:26:20 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:26:20 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:26:43 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:26:43 [系统状态] 所有自动线程已停止
2025-08-03 10:26:43 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:28:23 [系统状态] 检测到注册已暂停或终止，停止邮箱验证码处理
2025-08-03 10:28:57 [信息] 程序正在退出，开始清理工作...
2025-08-03 10:28:57 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 10:28:57 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 10:28:57 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 10:28:57 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 10:28:57 [信息] 程序退出清理工作完成
2025-08-03 10:46:48 [信息] AWS自动注册工具启动
2025-08-03 10:46:48 [信息] 程序版本: 1.0.0.0
2025-08-03 10:46:48 [信息] 启动时间: 2025-08-03 10:46:48
2025-08-03 10:46:48 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 10:46:48 [信息] 线程数量已选择: 1
2025-08-03 10:46:48 [信息] 线程数量选择初始化完成
2025-08-03 10:46:48 [信息] 程序初始化完成
2025-08-03 10:46:50 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 10:46:52 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:46:53 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:46:53 [信息] 成功加载 5 条数据
2025-08-03 10:46:55 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 10:46:55 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-03 10:46:56 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-03 10:46:56 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-03 10:46:56 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-03 10:46:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-03 10:46:56 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-03 10:46:57 [系统状态] 创建无痕模式上下文...
2025-08-03 10:46:59 [系统状态] 使用默认时区: America/New_York
2025-08-03 10:46:59 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-03 10:46:59 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 12核
2025-08-03 10:46:59 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=8 GB
2025-08-03 10:47:00 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-03 10:47:01 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1862x982
   • 可用区域: 1862x942

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: D79834F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.20
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-03 10:47:01 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1862x982    • 可用区域: 1862x942   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: D79834F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.20    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:47:01 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-03 10:47:01 [注册开始] 邮箱: <EMAIL>, 索引: 1/5
2025-08-03 10:47:01 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 10:47:01 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-03 10:47:01 [系统状态] 在现有窗口中新建标签页...
2025-08-03 10:47:01 [系统状态] 使用现有的浏览器上下文
2025-08-03 10:47:01 [系统状态] 正在新建标签页...
2025-08-03 10:47:01 [系统状态] 已设置页面视口大小为600x400
2025-08-03 10:47:01 [系统状态] 验证无痕Chrome模式状态...
2025-08-03 10:47:01 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-03 10:47:01 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-03 10:47:01 [系统状态] 正在打开AWS注册页面...
2025-08-03 10:47:31 [系统状态] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load"
2025-08-03 10:47:31 [错误] 注册过程出现错误 - 邮箱: <EMAIL>
2025-08-03 10:47:31 [系统状态] 第一页相关失败，数据保持不动
2025-08-03 10:47:31 [信息] 第一页相关失败，数据保持不动
2025-08-03 10:47:57 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:47:57 [系统状态]  继续注册被调用，当前状态: Error，当前步骤: 1
2025-08-03 10:47:57 [系统状态]  进行智能页面检测...
2025-08-03 10:47:57 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:47:57 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:47:57 [系统状态] 🎯 找到匹配按钮: 'Verify email address' → 第1页
2025-08-03 10:47:57 [系统状态] ✅ 直接确认为第1页
2025-08-03 10:47:57 [系统状态]  智能检测到当前在第1页
2025-08-03 10:47:57 [系统状态] 检测当前页面状态...
2025-08-03 10:47:57 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:47:57 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:47:57 [系统状态] 🎯 找到匹配按钮: 'Verify email address' → 第1页
2025-08-03 10:47:57 [系统状态] ✅ 直接确认为第1页
2025-08-03 10:47:57 [系统状态] 智能检测到当前在第1页，继续执行...
2025-08-03 10:47:57 [系统状态] 🔍 等待第一页加载完成...
2025-08-03 10:47:57 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:47:57 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:47:57 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-03 10:48:00 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-03 10:48:00 [系统状态] ✅ 未检测到IP异常错误，继续流程
2025-08-03 10:48:00 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-03 10:48:00 [系统状态] 🔍 第1次检测图形验证码...
2025-08-03 10:48:00 [系统状态] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 10:48:00 [系统状态] ⏳ 第1次未发现验证码，等待2秒后继续检测...
2025-08-03 10:48:02 [系统状态] 🔍 第2次检测图形验证码...
2025-08-03 10:48:02 [系统状态] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-03 10:48:02 [系统状态] ✅ 第2次检测发现图形验证码！
2025-08-03 10:48:02 [系统状态] 🔍 第一页图形验证码最终检测结果: 发现验证码
2025-08-03 10:48:02 [系统状态] ⚠️ 第一页检测到图形验证码，开始处理...
2025-08-03 10:48:02 [系统状态] 第一页图形验证码自动识别模式
2025-08-03 10:48:02 [系统状态] 第一页第1次尝试自动识别图形验证码...
2025-08-03 10:48:08 [系统状态] 第一页已从iframe截取验证码图片，大小: 34795 字节
2025-08-03 10:48:08 [系统状态] ✅ 图片验证通过：201x70px，34795字节，复杂度符合要求
2025-08-03 10:48:08 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 10:48:11 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"8643b8"},"taskId":"4abeaa1c-7014-11f0-a1bf-5254008382c7"}
2025-08-03 10:48:11 [系统状态] 第一页第1次识别结果: 8643b8 → 转换为小写: 8643b8
2025-08-03 10:48:11 [系统状态] 第一页使用iframe内GetByLabel选择器
2025-08-03 10:48:11 [系统状态] 已填入验证码: 8643b8
2025-08-03 10:48:11 [系统状态] 已点击iframe内Submit按钮
2025-08-03 10:48:13 [系统状态] 第一页第1次图形验证码识别成功
2025-08-03 10:48:13 [系统状态] 第一页图形验证码自动完成
2025-08-03 10:48:13 [系统状态] 📋 第一页图形验证码检查完成
2025-08-03 10:48:13 [系统状态] 第一页完成，等待验证码页面...
2025-08-03 10:48:14 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-03 10:48:14 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-03 10:48:14 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-03 10:48:28 [系统状态] 验证码获取成功: 802606，正在自动填入...
2025-08-03 10:48:28 [系统状态] 邮箱验证码获取成功，已取消获取线程
2025-08-03 10:48:28 [系统状态] 验证码已自动填入，正在自动点击验证按钮...
2025-08-03 10:48:28 [系统状态] 邮箱验证完成，等待页面跳转...
2025-08-03 10:48:31 [系统状态] 等待密码设置页面加载...
2025-08-03 10:48:31 [系统状态] 开始填写密码信息...
2025-08-03 10:48:31 [系统状态] 第一个密码输入框已清空并重新填写完成
2025-08-03 10:48:31 [系统状态] 确认密码输入框已清空并重新填写完成
2025-08-03 10:48:31 [系统状态] 密码填写完成，点击继续按钮...
2025-08-03 10:48:32 [系统状态] 密码设置完成，等待页面跳转...
2025-08-03 10:48:35 [系统状态] 第三页完成，进入第3.5页（账户类型确认页面）...
2025-08-03 10:48:35 [系统状态] 等待账户类型确认页面加载...
2025-08-03 10:48:35 [系统状态] 开始处理账户类型确认...
2025-08-03 10:48:53 [系统状态] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible
2025-08-03 10:49:34 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:49:34 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 3
2025-08-03 10:49:34 [系统状态]  进行智能页面检测...
2025-08-03 10:49:34 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:49:34 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:49:34 [系统状态] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页
2025-08-03 10:49:34 [系统状态] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮...
2025-08-03 10:49:35 [系统状态] 账户类型确认完成，进入第4页（联系信息页面）...
2025-08-03 10:49:59 [系统状态] 检测到注册已暂停或终止，停止后续操作
2025-08-03 10:49:59 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-08-03 10:49:59 [系统状态] 🔬 执行详细页面分析...
2025-08-03 10:49:59 [系统状态] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register&refid=em_127222&p=free&c=hp&z=1&redirect_url=https%3A%2F%2Faws.amazon.com%2Fregistration-confirmation#/account
2025-08-03 10:49:59 [系统状态] 📋 页面标题: AWS Console - Signup
2025-08-03 10:49:59 [系统状态] 📊 分析结果: 第四页-联系信息(3/4个元素匹配), 第六页-手机验证(1/3个元素匹配)
2025-08-03 10:49:59 [系统状态]  智能检测到当前在第6页
2025-08-03 10:49:59 [系统状态] 智能检测到当前在第6页，开始智能处理...
2025-08-03 10:49:59 [系统状态] 检测第6页手机验证状态...
2025-08-03 10:50:29 [系统状态] 第6页暂停处理失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Mobile phone number" })
2025-08-03 10:50:29 [系统状态] 开始填写验证码验证页面...
2025-08-03 10:50:29 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-03 10:50:50 [按钮操作] 暂停注册 -> 暂停当前注册流程
2025-08-03 10:50:50 [系统状态] 所有自动线程已停止
2025-08-03 10:50:50 [系统状态] 注册已暂停，所有自动线程已停止
2025-08-03 10:50:51 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-03 10:50:51 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 6
2025-08-03 10:50:51 [系统状态]  进行智能页面检测...
2025-08-03 10:50:51 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-03 10:50:51 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-03 10:50:51 [系统状态] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页
2025-08-03 10:50:51 [系统状态] ✅ 直接确认为第4页
2025-08-03 10:50:51 [系统状态]  智能检测到当前在第4页
2025-08-03 10:50:51 [系统状态] 智能检测到当前在第4页，开始智能处理...
2025-08-03 10:50:51 [系统状态] 开始后台获取手机号码，同时填写其他信息...
2025-08-03 10:50:51 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-03 10:50:51 [系统状态] 数据国家代码为VN，需要选择Vietnam
2025-08-03 10:50:52 [系统状态] 已点击国家/地区选择器，正在展开列表...
2025-08-03 10:50:52 [系统状态] 后台获取榴莲手机号码成功: +524981685682，已保存到注册数据
2025-08-03 10:50:53 [系统状态] 已选择国家: Vietnam
2025-08-03 10:50:53 [系统状态] 已成功选择国家: Vietnam
2025-08-03 10:50:53 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-03 10:50:53 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-03 10:50:56 [系统状态] 已选择国家代码 +52
2025-08-03 10:50:56 [系统状态] 等待后台获取的手机号码结果...
2025-08-03 10:50:56 [系统状态] 已自动获取并填入手机号码: +524981685682
2025-08-03 10:50:57 [系统状态] 使用已获取的手机号码: +524981685682（保存本地号码: 4981685682）
2025-08-03 10:50:58 [系统状态] 联系信息完成，等待页面加载...
2025-08-03 10:50:59 [系统状态] 选择国家代码失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Country or region code" })
2025-08-03 10:51:01 [系统状态] 进入付款信息页面...
2025-08-03 10:51:01 [系统状态] 正在选择月份: May
2025-08-03 10:51:02 [系统状态] 已选择月份（标准选项）: May
2025-08-03 10:51:02 [系统状态] 正在选择年份: 2028
2025-08-03 10:51:02 [系统状态] 已选择年份（标准选项）: 2028
2025-08-03 10:51:03 [系统状态] 付款信息完成，进入验证码验证页面...
2025-08-03 10:51:03 [系统状态] 开始填写验证码验证页面...
2025-08-03 10:51:03 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-03 10:51:09 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-03 10:51:09 [系统状态] 已清空并重新填写手机号码: 4981685682
2025-08-03 10:51:09 [系统状态] 已点击发送验证码按钮
2025-08-03 10:51:11 [系统状态] 🔍 检查是否出现验证手机区号错误...
2025-08-03 10:51:11 [系统状态] ✅ 未检测到验证手机区号错误，继续执行
2025-08-03 10:51:11 [系统状态] 手机号码自动模式 + 图形验证码自动模式：开始自动处理...
2025-08-03 10:51:11 [系统状态] 自动模式：开始处理图形验证码...
2025-08-03 10:51:11 [系统状态] 第六页点击发送验证码后，等待图形验证码出现...
2025-08-03 10:51:14 [系统状态] 第六页图形验证码自动识别模式，开始处理...
2025-08-03 10:51:14 [系统状态] 第六页第1次尝试自动识别图形验证码...
2025-08-03 10:51:21 [系统状态] 已选择国家代码: +52
2025-08-03 10:51:21 [系统状态] 已清空并重新填写手机号码: 4981685682
2025-08-03 10:51:21 [系统状态] 已点击发送验证码按钮
2025-08-03 10:51:23 [系统状态] 🔍 检查是否出现验证手机区号错误...
2025-08-03 10:51:23 [系统状态] ✅ 未检测到验证手机区号错误，继续执行
2025-08-03 10:51:23 [系统状态] 手机号码自动模式 + 图形验证码自动模式：开始自动处理...
2025-08-03 10:51:23 [系统状态] 自动模式：开始处理图形验证码...
2025-08-03 10:51:23 [系统状态] 第六页点击发送验证码后，等待图形验证码出现...
2025-08-03 10:51:26 [系统状态] 第六页图形验证码自动识别模式，开始处理...
2025-08-03 10:51:26 [系统状态] 第六页第1次尝试自动识别图形验证码...
2025-08-03 10:51:27 [系统状态] 第六页验证码元素等待超时，转为手动模式
2025-08-03 10:51:27 [系统状态] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理
2025-08-03 10:51:27 [系统状态] 第六页验证码元素等待超时，直接转为手动模式
2025-08-03 10:51:27 [系统状态] 第六页图形验证码自动处理失败，转为手动模式
2025-08-03 10:51:32 [系统状态] 第六页已从iframe截取验证码图片，大小: 35683 字节
2025-08-03 10:51:32 [系统状态] ✅ 图片验证通过：201x71px，35683字节，复杂度符合要求
2025-08-03 10:51:32 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-03 10:51:33 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"cxrdrg"},"taskId":"c2ff99aa-7014-11f0-a1bf-5254008382c7"}
2025-08-03 10:51:33 [系统状态] 第六页第1次识别结果: cxrdrg → 转换为小写: cxrdrg
2025-08-03 10:51:33 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-03 10:51:33 [系统状态] 第六页已填入验证码: cxrdrg
2025-08-03 10:51:33 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-03 10:51:36 [系统状态] 第1次图形验证码识别成功
2025-08-03 10:51:36 [系统状态] 第六页图形验证码自动完成，检查验证结果...
2025-08-03 10:51:39 [系统状态] 第六页图形验证码验证成功，进入第七页
2025-08-03 10:51:42 [系统状态] 开始处理第七页 - Continue (step 4 of 5) 页面
2025-08-03 10:51:42 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-03 10:51:42 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-03 10:51:42 [系统状态] 第七页自动模式：等待5秒后开始获取验证码...
2025-08-03 10:51:47 [系统状态] 开始自动获取验证码，2分钟超时...
2025-08-03 10:51:47 [系统状态] 第1次尝试获取验证码...（剩余4次尝试）
2025-08-03 10:51:47 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 10:51:57 [错误] [榴莲API] 获取验证码请求超时，尝试 1/4: The request was canceled due to the configured HttpClient.Timeout of 10 seconds elapsing.
2025-08-03 10:51:57 [信息] [榴莲API] 1000ms后重试获取验证码...
2025-08-03 10:51:58 [信息] [榴莲API] 获取验证码，尝试 2/4
2025-08-03 10:52:04 [系统状态] 成功获取验证码: 6924，立即填入验证码...
2025-08-03 10:52:04 [系统状态] 已自动填入手机验证码: 6924
2025-08-03 10:52:04 [系统状态] 榴莲手机号码已加入黑名单
2025-08-03 10:52:05 [系统状态] 正在自动点击Continue按钮...
2025-08-03 10:52:05 [系统状态] 手机验证码验证完成，继续执行后续步骤...
2025-08-03 10:52:08 [系统状态] 验证码验证完成，等待页面加载...
2025-08-03 10:52:09 [系统状态] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单失败: 请勿重复操作
2025-08-03 10:52:37 [系统状态] 点击完成注册按钮失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" })
2025-08-03 10:52:37 [系统状态] 等待完成注册页面加载...
2025-08-03 10:52:40 [按钮操作] 终止注册 -> 终止当前注册流程
2025-08-03 10:52:40 [系统状态] 所有自动线程已停止
2025-08-03 10:52:40 [信息] 检测到正在注册数据为0且有未处理数据，已重置状态并启用开始注册按钮
2025-08-03 10:52:40 [系统状态] 注册已终止
2025-08-03 10:52:40 [注册结束] 注册被终止 - 邮箱: <EMAIL>
2025-08-03 10:52:40 [系统状态] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Lpk3a8B9 ③AWS密码：2bIRIQMD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 10:52:40 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Lpk3a8B9 ③AWS密码：2bIRIQMD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 10:52:40 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：0Lpk3a8B9 ③AWS密码：2bIRIQMD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-03 10:52:40 [系统状态] 完成注册页面加载完成
2025-08-03 10:52:40 [系统状态] 注册已被终止，停止密钥提取流程
2025-08-03 10:52:40 [系统状态] 注册已被终止
2025-08-03 10:52:40 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-03 10:52:58 [信息] 线程数量已选择: 2
2025-08-03 10:53:08 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 10:53:08 [信息] 开始启动多线程注册，线程数量: 2
2025-08-03 10:53:08 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 4
2025-08-03 10:53:08 [信息] 所有线程已停止并清理
2025-08-03 10:53:08 [信息] 正在初始化多线程服务...
2025-08-03 10:53:08 [信息] 榴莲手机API服务已初始化
2025-08-03 10:53:08 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-03 10:53:08 [信息] 多线程服务初始化完成
2025-08-03 10:53:08 [信息] 数据分配完成：共4条数据分配给2个线程
2025-08-03 10:53:08 [信息] 线程1分配到2条数据
2025-08-03 10:53:08 [信息] 线程2分配到2条数据
2025-08-03 10:53:08 [信息] 屏幕工作区域: 1280x672
2025-08-03 10:53:08 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 10:53:08 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 10:53:08 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:53:08 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=12 GB
2025-08-03 10:53:08 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 10:53:08 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:53:09 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:53:09 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 10:53:09 [信息] 屏幕工作区域: 1280x672
2025-08-03 10:53:09 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 10:53:09 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 10:53:09 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:53:09 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=4 GB
2025-08-03 10:53:09 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-03 10:53:09 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:53:09 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:53:09 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 10:53:09 [信息] 多线程注册启动成功，共2个线程
2025-08-03 10:53:09 线程2：[信息] 开始启动注册流程
2025-08-03 10:53:09 线程1：[信息] 开始启动注册流程
2025-08-03 10:53:09 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 10:53:09 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-03 10:53:09 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 10:53:09 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 10:53:09 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 10:53:09 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 10:53:09 [信息] 多线程管理窗口已初始化
2025-08-03 10:53:09 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:53:09 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 10:53:09 [信息] 多线程管理窗口已打开
2025-08-03 10:53:09 [信息] 多线程注册启动成功，共2个线程
2025-08-03 10:53:12 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:53:12 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 10:53:12 线程2：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 10:53:12 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:53:12 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 10:53:12 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 10:53:12 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 10:53:12 线程1：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 10:53:12 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 10:53:12 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-03 10:53:12 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 10:53:12 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 10:53:13 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 10:53:13 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 10:53:15 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 10:53:15 线程1：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:53:15 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 20.8449, 106.6881 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=20.8449, 经度=106.6881
2025-08-03 10:53:15 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 10:53:15 线程2：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:53:15 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 21.0285, 105.8542 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=21.0285, 经度=105.8542
2025-08-03 10:53:15 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 24核 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=12 GB
2025-08-03 10:53:15 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 32核 (进度: 0%)
2025-08-03 10:53:15 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=4 GB
2025-08-03 10:53:17 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 10:53:17 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 10:53:17 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1923x1027
   • 可用区域: 1923x987

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.69
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 10:53:17 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1923x1027    • 可用区域: 1923x987   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.69    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:53:17 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 10:53:17 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 10:53:17 线程1：[信息] 浏览器启动成功
2025-08-03 10:53:17 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 10:53:17 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 10:53:17 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 2014x1085
   • 可用区域: 2014x1045

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.26
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 10:53:17 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 2014x1085    • 可用区域: 2014x1045   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.26    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:53:17 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 10:53:17 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 10:53:18 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 10:53:18 线程2：[信息] 浏览器启动成功
2025-08-03 10:53:18 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 10:53:18 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 10:53:18 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-03 10:53:18 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 10:53:18 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 10:53:43 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 10:53:43 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 10:53:43 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 10:53:43 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 10:53:43 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:53:43 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-03 10:53:43 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 10:53:43 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 10:53:43 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 10:53:43 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-03 10:53:43 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:53:44 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-03 10:53:46 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 10:53:46 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 10:53:46 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 10:53:46 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 10:53:46 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:53:46 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 10:53:47 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-03 10:53:47 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 10:53:47 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 10:53:47 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 10:53:47 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:53:47 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 10:53:48 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 10:53:49 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 10:53:49 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:53:53 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34955 字节 (进度: 100%)
2025-08-03 10:53:53 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34955字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:53:53 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:53:54 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35010 字节 (进度: 100%)
2025-08-03 10:53:54 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35010字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:53:54 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:53:55 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"nwcr7g"},"taskId":"1766248c-7015-11f0-bf43-7234de99a3d9"} (进度: 100%)
2025-08-03 10:53:55 线程2：[信息] [信息] 第一页第1次识别结果: nwcr7g → 转换为小写: nwcr7g (进度: 100%)
2025-08-03 10:53:55 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:53:55 线程2：[信息] [信息] 已填入验证码: nwcr7g (进度: 100%)
2025-08-03 10:53:56 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:53:56 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"yfbdmz"},"taskId":"180e2362-7015-11f0-bc85-2e23c0b40dc6"} (进度: 100%)
2025-08-03 10:53:56 线程1：[信息] [信息] 第一页第1次识别结果: yfbdmz → 转换为小写: yfbdmz (进度: 100%)
2025-08-03 10:53:56 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:53:56 线程1：[信息] [信息] 已填入验证码: yfbdmz (进度: 100%)
2025-08-03 10:53:56 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:53:58 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 10:53:58 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-03 10:53:58 线程1：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 10:53:58 线程1：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-03 10:54:00 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:54:00 线程1：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:54:03 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31239 字节 (进度: 100%)
2025-08-03 10:54:03 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31239字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:54:03 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:54:08 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fh76hp"},"taskId":"1f737ac6-7015-11f0-bf43-7234de99a3d9"} (进度: 100%)
2025-08-03 10:54:08 线程2：[信息] [信息] 第一页第2次识别结果: fh76hp → 转换为小写: fh76hp (进度: 100%)
2025-08-03 10:54:08 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:54:08 线程2：[信息] [信息] 已填入验证码: fh76hp (进度: 100%)
2025-08-03 10:54:08 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 第一页第2次图形验证码识别成功 (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:54:10 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 10:54:10 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 10:54:10 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:54:10 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-03 10:54:13 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:13 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:13
2025-08-03 10:54:13 线程1：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-03 10:54:13 线程1：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-03 10:54:13 线程1：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:54:14 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-03 10:54:14 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 10:54:14 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:54:14 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 10:54:16 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:16 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:16
2025-08-03 10:54:16 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:16 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:16
2025-08-03 10:54:19 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:19 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:19
2025-08-03 10:54:19 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:19
2025-08-03 10:54:22 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:22 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:22
2025-08-03 10:54:23 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:23
2025-08-03 10:54:26 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:26 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:26
2025-08-03 10:54:26 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:26
2025-08-03 10:54:29 [信息] [线程2] 第6次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:29 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:29
2025-08-03 10:54:29 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:29
2025-08-03 10:54:32 [信息] [线程2] 第7次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:32 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:32
2025-08-03 10:54:32 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:32 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:32
2025-08-03 10:54:35 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:35 [信息] [线程2] 第8次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:35 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:35
2025-08-03 10:54:35 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:35
2025-08-03 10:54:38 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:38 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:38
2025-08-03 10:54:38 [信息] [线程2] 第9次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:38 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:38
2025-08-03 10:54:42 [信息] [线程1] 第9次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:42 [信息] [线程2] 第10次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:42 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:42
2025-08-03 10:54:42 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:42
2025-08-03 10:54:45 [信息] [线程1] 第10次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:45 [信息] [线程2] 第11次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:45 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:45
2025-08-03 10:54:45 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:45
2025-08-03 10:54:48 [信息] [线程1] 第11次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:48 [信息] [线程2] 第12次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:48 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:48
2025-08-03 10:54:48 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:48
2025-08-03 10:54:52 [信息] [线程1] 第12次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:52 [信息] [线程2] 第13次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:52 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:52
2025-08-03 10:54:52 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:52
2025-08-03 10:54:55 [信息] [线程1] 第13次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:55 [信息] [线程2] 第14次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:55 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:55
2025-08-03 10:54:55 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:55
2025-08-03 10:54:58 [信息] [线程1] 第14次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:58 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:54:58
2025-08-03 10:54:58 [信息] [线程2] 第15次触发邮箱验证码获取...（最多20次）
2025-08-03 10:54:58 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:54:58
2025-08-03 10:54:59 [信息] 多线程窗口引用已清理
2025-08-03 10:54:59 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 10:54:59 [信息] 多线程管理窗口正在关闭
2025-08-03 10:55:00 [信息] 程序正在退出，开始清理工作...
2025-08-03 10:55:00 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 10:55:00 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 10:55:00 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 10:55:00 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 10:55:00 [信息] 程序退出清理工作完成
2025-08-03 10:56:00 [信息] AWS自动注册工具启动
2025-08-03 10:56:00 [信息] 程序版本: 1.0.0.0
2025-08-03 10:56:00 [信息] 启动时间: 2025-08-03 10:56:00
2025-08-03 10:56:01 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-03 10:56:01 [信息] 线程数量已选择: 1
2025-08-03 10:56:01 [信息] 线程数量选择初始化完成
2025-08-03 10:56:01 [信息] 程序初始化完成
2025-08-03 10:56:06 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 10:56:09 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南 - mail.txt
2025-08-03 10:56:10 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-03 10:56:11 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:56:12 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-02-越南.txt
2025-08-03 10:56:12 [信息] 成功加载 4 条数据
2025-08-03 10:56:13 [信息] 线程数量已选择: 2
2025-08-03 10:56:15 [按钮操作] 开始注册 -> 启动注册流程
2025-08-03 10:56:15 [信息] 开始启动多线程注册，线程数量: 2
2025-08-03 10:56:15 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 4
2025-08-03 10:56:15 [信息] 所有线程已停止并清理
2025-08-03 10:56:15 [信息] 正在初始化多线程服务...
2025-08-03 10:56:15 [信息] 榴莲手机API服务已初始化
2025-08-03 10:56:15 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-03 10:56:15 [信息] 多线程服务初始化完成
2025-08-03 10:56:15 [信息] 数据分配完成：共4条数据分配给2个线程
2025-08-03 10:56:15 [信息] 线程1分配到2条数据
2025-08-03 10:56:15 [信息] 线程2分配到2条数据
2025-08-03 10:56:15 [信息] 屏幕工作区域: 1280x672
2025-08-03 10:56:15 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 10:56:15 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 10:56:15 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:56:15 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=6 GB
2025-08-03 10:56:15 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-03 10:56:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:56:15 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:56:15 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 10:56:15 [信息] 屏幕工作区域: 1280x672
2025-08-03 10:56:15 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-03 10:56:15 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=VN
2025-08-03 10:56:15 [信息] 为国家代码 VN 生成智能指纹: 时区=Asia/Ho_Chi_Minh, 语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:56:15 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=8 GB
2025-08-03 10:56:15 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-03 10:56:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:56:15 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-03 10:56:15 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=VN, 时区=Asia/Ho_Chi_Minh
2025-08-03 10:56:15 [信息] 多线程注册启动成功，共2个线程
2025-08-03 10:56:15 线程1：[信息] 开始启动注册流程
2025-08-03 10:56:15 线程2：[信息] 开始启动注册流程
2025-08-03 10:56:15 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-03 10:56:15 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-03 10:56:15 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-03 10:56:15 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-03 10:56:15 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 10:56:15 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-03 10:56:15 [信息] 多线程管理窗口已初始化
2025-08-03 10:56:15 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:56:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-03 10:56:15 [信息] 多线程管理窗口已打开
2025-08-03 10:56:15 [信息] 多线程注册启动成功，共2个线程
2025-08-03 10:56:17 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:56:17 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-03 10:56:17 线程2：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 10:56:17 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 10:56:17 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-03 10:56:17 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 10:56:18 [信息] UniformGrid列数已更新为: 1
2025-08-03 10:56:18 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-03 10:56:18 线程1：[信息] [信息] 多线程模式根据指纹国家代码 VN 设置浏览器语言: Tiếng Việt (进度: 0%)
2025-08-03 10:56:18 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=VN, 语言=Tiếng Việt, 参数=--lang=vi-VN
2025-08-03 10:56:18 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-03 10:56:18 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-03 10:56:19 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 10:56:19 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-03 10:56:21 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 10:56:21 线程1：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:56:21 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 10.0452, 105.7469 (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=10.0452, 经度=105.7469
2025-08-03 10:56:21 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 4核 (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=6 GB
2025-08-03 10:56:21 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Ho_Chi_Minh (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Ho_Chi_Minh
2025-08-03 10:56:21 线程2：[信息] [信息] 多线程模式使用指纹语言: vi-VN,vi;q=0.9,en;q=0.8 (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器语言设置: 多线程模式使用指纹语言=vi-VN,vi;q=0.9,en;q=0.8
2025-08-03 10:56:21 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 10.0452, 105.7469 (进度: 0%)
2025-08-03 10:56:21 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=10.0452, 经度=105.7469
2025-08-03 10:56:23 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 10:56:23 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 24核 (进度: 0%)
2025-08-03 10:56:23 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=8 GB
2025-08-03 10:56:24 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-03 10:56:25 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1989x1072
   • 可用区域: 1989x1032

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.81
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 10:56:25 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1989x1072    • 可用区域: 1989x1032   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.81    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:56:25 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1766x1125
   • 可用区域: 1766x1085

🌍 地区语言信息:
   • 主语言: vi-VN
   • 语言列表: vi-VN,en-US
   • 时区偏移: -480分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.47
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-03 10:56:25 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1766x1125    • 可用区域: 1766x1085   地区语言信息:    • 主语言: vi-VN    • 语言列表: vi-VN,en-US    • 时区偏移: -480分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.47    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-03 10:56:25 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-03 10:56:25 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-03 10:56:25 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 10:56:25 线程1：[信息] 浏览器启动成功
2025-08-03 10:56:25 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-03 10:56:25 线程2：[信息] 浏览器启动成功
2025-08-03 10:56:25 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-03 10:56:25 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-03 10:56:25 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-03 10:56:25 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-03 10:56:25 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 10:56:25 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-03 10:56:25 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 10:56:25 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-03 10:56:25 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 10:56:25 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-03 10:56:25 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 10:56:25 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-03 10:56:25 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 10:56:25 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-03 10:56:26 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 10:56:26 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-03 10:56:26 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-03 10:56:26 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-03 10:56:29 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-03 10:56:29 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-03 10:56:29 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-03 10:56:53 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-03 10:56:53 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-03 10:56:53 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-03 10:56:56 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-03 10:56:56 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-03 10:56:56 [信息] 第一页相关失败，数据保持不动
2025-08-03 10:56:56 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-03 10:56:56 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 10:57:23 线程2：[信息] [信息] ❌ 第一页加载超时，未找到验证邮箱按钮 (进度: 98%)
2025-08-03 10:57:23 [信息] 第一页加载超时，未找到验证邮箱按钮
2025-08-03 10:57:23 线程2：[信息] [信息] ❌ 第一页执行异常: 第一页加载超时，未找到验证邮箱按钮 (进度: 98%)
2025-08-03 10:57:23 线程2：[信息] [信息] 注册失败: 第一页执行失败: 第一页加载超时，未找到验证邮箱按钮 (进度: 98%)
2025-08-03 10:57:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:23 [信息] 多线程状态已重置
2025-08-03 10:57:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:23 [信息] 多线程状态已重置
2025-08-03 10:57:23 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-03 10:57:23 [信息] 第一页相关失败，数据保持不动
2025-08-03 10:57:23 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:23 [信息] 多线程状态已重置
2025-08-03 10:57:23 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-03 10:57:23 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-03 10:57:53 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-03 10:57:53 [信息] 多线程状态已重置
2025-08-03 10:57:53 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-03 10:57:53 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-03 10:57:53 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:57:53 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-03 10:57:53 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-03 10:57:53 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-03 10:57:53 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-03 10:57:53 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-03 10:57:56 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-03 10:57:56 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 10:57:56 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 10:57:56 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 10:57:56 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-03 10:57:56 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-03 10:57:56 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-03 10:57:57 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-03 10:57:57 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:57:57 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 10:57:57 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:57:57 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-03 10:57:59 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:57:59 线程1：[信息] 已继续
2025-08-03 10:57:59 [信息] 线程1已继续
2025-08-03 10:57:59 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:57:59 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 10:58:01 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:01 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:01
2025-08-03 10:58:04 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:04 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:04
2025-08-03 10:58:07 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:07 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:07
2025-08-03 10:58:10 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35624 字节 (进度: 100%)
2025-08-03 10:58:10 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35624字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:58:10 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:58:10 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:10 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:10
2025-08-03 10:58:12 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ur2yw4"},"taskId":"b09dc15a-7015-11f0-8c61-861c57ef4db4"} (进度: 100%)
2025-08-03 10:58:12 线程2：[信息] [信息] 第一页第1次识别结果: ur2yw4 → 转换为小写: ur2yw4 (进度: 100%)
2025-08-03 10:58:12 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:58:12 线程2：[信息] [信息] 已填入验证码: ur2yw4 (进度: 100%)
2025-08-03 10:58:12 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:58:13 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:13 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:13
2025-08-03 10:58:14 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 10:58:14 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-03 10:58:16 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:58:16 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:16 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:16
2025-08-03 10:58:19 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31476 字节 (进度: 100%)
2025-08-03 10:58:19 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31476字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:58:19 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:58:19 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:19 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:19
2025-08-03 10:58:22 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"hnnpfx"},"taskId":"b67251b8-7015-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 10:58:22 线程2：[信息] [信息] 第一页第2次识别结果: hnnpfx → 转换为小写: hnnpfx (进度: 100%)
2025-08-03 10:58:22 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:58:22 线程2：[信息] [信息] 已填入验证码: hnnpfx (进度: 100%)
2025-08-03 10:58:22 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:58:22 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:58:22
2025-08-03 10:58:23 [信息] [线程1] 邮箱验证码获取成功: 511846，立即停止重复请求
2025-08-03 10:58:23 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 10:58:23 [信息] [线程1] 已清理响应文件
2025-08-03 10:58:23 线程1：[信息] [信息] 验证码获取成功: 511846，正在自动填入... (进度: 100%)
2025-08-03 10:58:23 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 第一页第2次图形验证码识别成功 (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:58:24 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:58:24 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:58:24 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:58:24 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-03 10:58:24 线程2：[信息] 已继续
2025-08-03 10:58:24 [信息] 线程2已继续
2025-08-03 10:58:24 [信息] 继续了 2 个可继续的线程
2025-08-03 10:58:26 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:26 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:58:26
2025-08-03 10:58:29 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:29 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:58:29
2025-08-03 10:58:32 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:32 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:58:32
2025-08-03 10:58:35 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-03 10:58:35 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-03 10:58:35
2025-08-03 10:58:36 [信息] [线程2] 邮箱验证码获取成功: 570512，立即停止重复请求
2025-08-03 10:58:36 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-03 10:58:36 [信息] [线程2] 已清理响应文件
2025-08-03 10:58:36 线程2：[信息] [信息] 验证码获取成功: 570512，正在自动填入... (进度: 100%)
2025-08-03 10:58:36 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-03 10:58:36 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-03 10:58:36 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-03 10:58:36 [信息] 线程2完成第二页事件已处理
2025-08-03 10:58:36 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-03 10:58:36 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-03 10:58:36 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-03 10:58:36 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-03 10:58:38 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+522281628100","+526636696626"]}
2025-08-03 10:58:38 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-03 10:58:38 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-03 10:58:38 [信息] 线程1分配榴莲手机号码: +522281628100
2025-08-03 10:58:38 [信息] 线程2分配榴莲手机号码: +526636696626
2025-08-03 10:58:38 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-03 10:58:38 [信息] 批量获取2个手机号码成功
2025-08-03 10:58:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-03 10:58:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-03 10:58:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-03 10:58:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-03 10:58:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-03 10:58:40 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-03 10:58:43 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-03 10:58:43 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-03 10:58:43 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-03 10:58:51 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-03 10:58:51 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-03 10:58:53 线程1：[信息] [信息] 自动获取邮箱验证码出错: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Verification code" }) (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForVerification，当前步骤: 2 (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 点击验证按钮... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 验证码验证完成，等待页面跳转... (进度: 100%)
2025-08-03 10:59:08 线程1：[信息] [信息] 已彻底取消邮箱验证码获取线程 (进度: 100%)
2025-08-03 10:59:08 [信息] 线程1完成第二页事件已处理
2025-08-03 10:59:08 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-03 10:59:08 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-03 10:59:11 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-03 10:59:11 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-03 10:59:16 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 10:59:16 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 10:59:16 线程1：[信息] 已暂停
2025-08-03 10:59:16 [信息] 线程1已暂停
2025-08-03 10:59:16 [信息] 线程1已暂停
2025-08-03 10:59:16 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-03 10:59:16 [信息] 线程2获取已分配的榴莲手机号码: +526636696626
2025-08-03 10:59:16 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526636696626 (进度: 100%)
2025-08-03 10:59:17 线程2：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 100%)
2025-08-03 10:59:17 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 智能检测到当前在第2页，开始智能处理... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 智能检测到第2页，检查邮箱验证码模式... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 邮箱验证码自动模式，继续自动执行... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:59:19 线程1：[信息] 已继续
2025-08-03 10:59:19 [信息] 线程1已继续
2025-08-03 10:59:19 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:59:19 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 10:59:19 线程2：[信息] [信息] 已选择国家: Vietnam (进度: 100%)
2025-08-03 10:59:19 线程2：[信息] [信息] 已成功选择国家: Vietnam (进度: 100%)
2025-08-03 10:59:19 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 10:59:19 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 10:59:20 线程1：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-03 10:59:20 线程1：[信息] 已继续
2025-08-03 10:59:20 [信息] 线程1已继续
2025-08-03 10:59:21 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:59:21 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:59:21
2025-08-03 10:59:21 [信息] [线程1] 邮箱验证码获取成功: 511846，立即停止重复请求
2025-08-03 10:59:21 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 10:59:21 [信息] [线程1] 已清理响应文件
2025-08-03 10:59:21 线程1：[信息] [信息] 检测到注册已暂停或终止，停止邮箱验证码处理 (进度: 100%)
2025-08-03 10:59:22 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-03 10:59:23 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-03 10:59:23 线程2：[信息] [信息] 已自动获取并填入手机号码: +526636696626 (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 2 (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify' → 第2页 (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 🔍 疑似第2页，进行二次确认... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] ✅ 确认为第2页：找到Verification code输入框 (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息]  智能检测到当前在第2页 (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 智能检测到当前在第2页，开始智能处理... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 智能检测到第2页，检查邮箱验证码模式... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 邮箱验证码自动模式，继续自动执行... (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-03 10:59:24 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-03 10:59:24 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-03 10:59:24 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-03 10:59:24 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-03 10:59:24 线程1：[信息] 已继续
2025-08-03 10:59:24 [信息] 线程1已继续
2025-08-03 10:59:24 线程2：[信息] [信息] 使用已获取的手机号码: +526636696626（保存本地号码: +526636696626） (进度: 100%)
2025-08-03 10:59:24 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-03 10:59:26 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-03 10:59:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-03 10:59:26
2025-08-03 10:59:26 [信息] [线程1] 邮箱验证码获取成功: 511846，立即停止重复请求
2025-08-03 10:59:26 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-03 10:59:26 [信息] [线程1] 已清理响应文件
2025-08-03 10:59:26 线程1：[信息] [信息] 验证码获取成功: 511846，正在自动填入... (进度: 100%)
2025-08-03 10:59:26 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-03 10:59:27 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-03 10:59:27 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-03 10:59:27 [信息] 线程1完成第二页事件已处理
2025-08-03 10:59:27 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-03 10:59:27 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-03 10:59:28 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-03 10:59:29 线程2：[信息] [信息] 正在选择月份: December (进度: 100%)
2025-08-03 10:59:29 线程2：[信息] [信息] 已选择月份（标准选项）: December (进度: 100%)
2025-08-03 10:59:30 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-03 10:59:30 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-03 10:59:30 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-03 10:59:30 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-03 10:59:30 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-03 10:59:30 线程1：[信息] 已暂停
2025-08-03 10:59:30 [信息] 线程1已暂停
2025-08-03 10:59:30 [信息] 线程1已暂停
2025-08-03 10:59:31 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-03 10:59:31 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-03 10:59:31 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-03 10:59:31 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 10:59:36 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 10:59:37 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-03 10:59:37 线程2：[信息] [信息] 已清空并重新填写手机号码: +526636696626 (进度: 100%)
2025-08-03 10:59:38 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-03 10:59:39 线程1：[信息] [信息] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible (进度: 100%)
2025-08-03 10:59:40 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 10:59:40 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 10:59:40 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 10:59:40 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 10:59:40 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 10:59:43 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 10:59:43 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 10:59:46 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34849 字节 (进度: 100%)
2025-08-03 10:59:46 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34849字节，复杂度符合要求 (进度: 100%)
2025-08-03 10:59:46 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 10:59:48 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"rmndcp"},"taskId":"e9ae48ca-7015-11f0-a1bf-5254008382c7"} (进度: 100%)
2025-08-03 10:59:48 线程2：[信息] [信息] 第六页第1次识别结果: rmndcp → 转换为小写: rmndcp (进度: 100%)
2025-08-03 10:59:48 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 10:59:48 线程2：[信息] [信息] 第六页已填入验证码: rmndcp (进度: 100%)
2025-08-03 10:59:48 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 10:59:51 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 10:59:51 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 10:59:54 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 10:59:57 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 10:59:57 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 10:59:57 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 10:59:57 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 11:00:02 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-03 11:00:02 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 11:00:02 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 11:00:02 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:00:05 线程2：[信息] [信息] 线程2验证码获取成功: 7738 (进度: 100%)
2025-08-03 11:00:05 [信息] 线程2手机号码已加入释放队列: +526636696626 (原因: 获取验证码成功)
2025-08-03 11:00:05 线程2：[信息] [信息] 线程2验证码获取成功: 7738，立即填入验证码... (进度: 100%)
2025-08-03 11:00:05 线程2：[信息] [信息] 线程2已自动填入手机验证码: 7738 (进度: 100%)
2025-08-03 11:00:06 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-03 11:00:06 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 11:00:09 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-03 11:00:09 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-03 11:00:09 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-03 11:00:09 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:00:09 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:00:09 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:00:09 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:00:09 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：7Up8659S ③AWS密码：4rlGnkFe ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:00:09 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:00:09 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:00:09 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-03 11:00:09 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:00:09 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-03 11:00:09 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-03 11:00:15 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 11:00:15 [信息] 开始释放1个手机号码
2025-08-03 11:00:15 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 11:00:15 [信息] [手机API] 释放手机号码: +526636696626
2025-08-03 11:00:15 [信息] [手机API] 手机号码释放成功: +526636696626
2025-08-03 11:00:16 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 11:00:16 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 11:00:16 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 📄 页面URL: https://signin.aws.amazon.com/signup?request_type=register (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 📊 分析结果: 第三页-密码设置(2/3个元素匹配) (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息]  智能检测到当前在第3页 (进度: 100%)
2025-08-03 11:00:16 线程1：[信息] [信息] 智能检测到当前在第3页，开始智能处理... (进度: 100%)
2025-08-03 11:00:22 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-03 11:00:22 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-03 11:00:22 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-03 11:00:22 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-03 11:00:22 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-03 11:00:23 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-03 11:00:26 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-03 11:00:26 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-03 11:00:26 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-03 11:00:42 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-03 11:00:42 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-03 11:01:07 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-03 11:01:07 [信息] 线程1获取已分配的榴莲手机号码: +522281628100
2025-08-03 11:01:07 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +522281628100 (进度: 100%)
2025-08-03 11:01:08 线程1：[信息] [信息] 数据国家代码为VN，需要选择Vietnam (进度: 100%)
2025-08-03 11:01:13 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-03 11:01:15 线程1：[信息] [信息] 已选择国家: Vietnam (进度: 100%)
2025-08-03 11:01:15 线程1：[信息] [信息] 已成功选择国家: Vietnam (进度: 100%)
2025-08-03 11:01:15 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 11:01:15 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 11:01:21 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-03 11:01:22 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-03 11:01:22 线程1：[信息] [信息] 已自动获取并填入手机号码: +522281628100 (进度: 100%)
2025-08-03 11:01:23 线程1：[信息] [信息] 使用已获取的手机号码: +522281628100（保存本地号码: +522281628100） (进度: 100%)
2025-08-03 11:01:23 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-03 11:01:27 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-03 11:01:28 线程1：[信息] [信息] 正在选择月份: March (进度: 100%)
2025-08-03 11:01:29 线程1：[信息] [信息] 已选择月份（标准选项）: March (进度: 100%)
2025-08-03 11:01:29 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-03 11:01:30 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-03 11:01:30 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-03 11:01:30 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-03 11:01:30 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-03 11:01:37 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-03 11:01:38 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-03 11:01:38 线程1：[信息] [信息] 已清空并重新填写手机号码: +522281628100 (进度: 100%)
2025-08-03 11:01:38 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-03 11:01:40 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 11:01:40 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 11:01:40 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-03 11:01:40 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 11:01:40 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 11:01:43 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 11:01:43 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 11:01:47 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35058 字节 (进度: 100%)
2025-08-03 11:01:47 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35058字节，复杂度符合要求 (进度: 100%)
2025-08-03 11:01:47 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 11:01:51 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"54m884"},"taskId":"3327ceea-7016-11f0-9058-7234de99a3d9"} (进度: 100%)
2025-08-03 11:01:51 线程1：[信息] [信息] 第六页第1次识别结果: 54m884 → 转换为小写: 54m884 (进度: 100%)
2025-08-03 11:01:51 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 11:01:51 线程1：[信息] [信息] 第六页已填入验证码: 54m884 (进度: 100%)
2025-08-03 11:01:51 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 11:01:55 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 11:01:55 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-03 11:01:57 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 11:02:01 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30983 字节 (进度: 100%)
2025-08-03 11:02:01 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，30983字节，复杂度符合要求 (进度: 100%)
2025-08-03 11:02:01 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 11:02:02 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2264"},"taskId":"3a0ee7ca-7016-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 11:02:02 线程1：[信息] [信息] 第六页第2次识别结果: 2264 → 转换为小写: 2264 (进度: 100%)
2025-08-03 11:02:02 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 11:02:03 线程1：[信息] [信息] 第六页已填入验证码: 2264 (进度: 100%)
2025-08-03 11:02:03 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 11:02:07 线程1：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 11:02:07 线程1：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-03 11:02:09 线程1：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 11:02:12 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31664 字节 (进度: 100%)
2025-08-03 11:02:12 线程1：[信息] [信息] ✅ 图片验证通过：200x71px，31664字节，复杂度符合要求 (进度: 100%)
2025-08-03 11:02:12 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 11:02:20 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"4nzac6"},"taskId":"446a0790-7016-11f0-8c61-861c57ef4db4"} (进度: 100%)
2025-08-03 11:02:20 线程1：[信息] [信息] 第六页第3次识别结果: 4nzac6 → 转换为小写: 4nzac6 (进度: 100%)
2025-08-03 11:02:20 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 11:02:20 线程1：[信息] [信息] 第六页已填入验证码: 4nzac6 (进度: 100%)
2025-08-03 11:02:20 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 11:02:24 线程1：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-03 11:02:24 线程1：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-08-03 11:02:24 线程1：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-03 11:02:24 线程1：[信息] 已继续
2025-08-03 11:02:24 [信息] 线程1已继续
2025-08-03 11:02:34 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-03 11:02:34 线程1：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-03 11:02:34 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-03 11:02:34 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 11:02:35 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 11:02:40 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 11:02:40 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 11:02:40 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 11:02:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:02:41 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:02:41 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:02:41 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:02:50 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-03 11:02:50 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:02:50 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:02:50 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:02:50 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:02:58 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-03 11:02:58 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:02:59 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:02:59 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:02:59 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:03:07 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-03 11:03:07 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:03:08 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:03:08 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:03:08 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:03:16 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-03 11:03:16 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:03:17 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:03:17 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:03:17 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:03:25 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-03 11:03:25 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:03:25 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:03:25 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:03:25 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:03:33 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-03 11:03:33 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:03:34 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:03:34 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:03:34 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-03 11:03:42 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-03 11:03:42 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:03:43 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-03 11:03:43 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-03 11:03:43 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-08-03 11:03:43 [信息] 线程1手机号码已加入释放队列: +522281628100 (原因: 验证码获取失败)
2025-08-03 11:03:43 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-03 11:03:43 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-03 11:03:43 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-03 11:03:43 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-03 11:03:43 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-03 11:03:45 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-03 11:03:45 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 11:03:45 [信息] 开始释放1个手机号码
2025-08-03 11:03:45 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 11:03:45 [信息] [手机API] 释放手机号码: +522281628100
2025-08-03 11:03:46 [信息] [手机API] 手机号码释放成功: +522281628100
2025-08-03 11:03:49 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 11:03:49 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 11:03:49 线程1：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-03 11:03:49 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-03 11:03:51 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-03 11:03:51 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-03 11:03:51 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-03 11:03:53 线程1：[信息] [信息] 后台获取新手机号码成功: +526645471650，已保存到注册数据 (进度: 100%)
2025-08-03 11:03:53 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-03 11:03:55 线程1：[信息] [信息] 已填入新手机号码: +526645471650 (进度: 100%)
2025-08-03 11:03:55 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-03 11:03:57 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-03 11:03:57 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-03 11:03:58 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-03 11:03:58 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-03 11:04:01 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-03 11:04:01 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-03 11:04:05 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35111 字节 (进度: 100%)
2025-08-03 11:04:05 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35111字节，复杂度符合要求 (进度: 100%)
2025-08-03 11:04:05 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-03 11:04:07 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7s646s"},"taskId":"841a70c8-7016-11f0-bc81-861c57ef4db4"} (进度: 100%)
2025-08-03 11:04:07 线程1：[信息] [信息] 第六页第1次识别结果: 7s646s → 转换为小写: 7s646s (进度: 100%)
2025-08-03 11:04:07 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-03 11:04:07 线程1：[信息] [信息] 第六页已填入验证码: 7s646s (进度: 100%)
2025-08-03 11:04:13 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-03 11:04:16 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-03 11:04:16 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-03 11:04:20 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-03 11:04:23 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-03 11:04:23 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 11:04:23 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-03 11:04:23 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-03 11:04:28 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-03 11:04:28 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-03 11:04:28 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-03 11:04:28 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-03 11:04:29 线程1：[信息] [信息] 线程1验证码获取成功: 5169 (进度: 100%)
2025-08-03 11:04:29 [信息] 线程1手机号码已加入释放队列: +526645471650 (原因: 获取验证码成功)
2025-08-03 11:04:29 线程1：[信息] [信息] 线程1验证码获取成功: 5169，立即填入验证码... (进度: 100%)
2025-08-03 11:04:29 线程1：[信息] [信息] 线程1已自动填入手机验证码: 5169 (进度: 100%)
2025-08-03 11:04:30 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-03 11:04:31 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-03 11:04:34 线程1：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-03 11:04:34 线程1：[信息] [信息] 线程1检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-03 11:04:34 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-03 11:04:34 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:04:34 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:04:34 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:04:34 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:04:34 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：9WBH0xU72s8 ③AWS密码：An3WpU5i ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-03 11:04:34 线程1：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:04:34 [信息] 线程1请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:04:34 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-03 11:04:34 [信息] 线程1失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-03 11:04:34 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-03 11:04:34 线程1：[信息] [信息] 线程1注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-03 11:04:34 线程1：[信息] 已继续
2025-08-03 11:04:34 [信息] 线程1已继续
2025-08-03 11:04:45 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-03 11:04:45 [信息] 开始释放1个手机号码
2025-08-03 11:04:45 [信息] [手机API] 开始批量释放1个手机号码
2025-08-03 11:04:45 [信息] [手机API] 释放手机号码: +526645471650
2025-08-03 11:04:45 [信息] [手机API] 手机号码释放成功: +526645471650
2025-08-03 11:04:46 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-03 11:04:46 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-03 11:06:08 [信息] 多线程窗口引用已清理
2025-08-03 11:06:08 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-03 11:06:08 [信息] 多线程管理窗口正在关闭
2025-08-03 11:06:09 [信息] 程序正在退出，开始清理工作...
2025-08-03 11:06:09 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-03 11:06:09 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-03 11:06:09 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-03 11:06:09 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-03 11:06:09 [信息] 程序退出清理工作完成
