[2025-08-03 09:45:38] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-08-03 09:45:38] [INFO] === AWS验证码获取工具启动 ===
[2025-08-03 09:45:38] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-08-03 09:45:38] [INFO] 系统平台: win32
[2025-08-03 09:45:38] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-08-03 09:45:39] [INFO] AWS文件监控已启动，监控当前目录
[2025-08-03 09:45:40] [INFO] 软件已初始化，请按照上方指南操作
[2025-08-03 09:45:52] [INFO] 更新剩余账户数量: 10
[2025-08-03 09:45:52] [INFO] 已选择文件: C:/Users/<USER>/Desktop/2025-08-02-越南 - mail.txt
[2025-08-03 09:45:54] [INFO] 解析结果: 未使用账户 10个, 成功账户 0个, 失败账户 0个
[2025-08-03 09:45:54] [INFO] 从文件中解析出 10 个账户
[2025-08-03 09:45:54] [DEBUG] 已排除 0 个成功登录的账户
[2025-08-03 09:45:54] [DEBUG] 已排除 0 个登录失败的账户
[2025-08-03 09:45:54] [DEBUG] 筛选后可用账户数量: 10
[2025-08-03 09:45:54] [INFO] 开始处理 1 个账户
[2025-08-03 09:45:54] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-08-03 09:45:55] [ERROR] 账户 <EMAIL> 获取访问令牌失败
[2025-08-03 09:45:55] [ERROR] 账户 <EMAIL> 已标记为登录失败
[2025-08-03 09:45:55] [ERROR] 更新进度时出错: cannot access local variable 'status_item' where it is not associated with a value
[2025-08-03 09:45:55] [SUCCESS] 处理完成，成功登录 0 个账户
[2025-08-03 09:45:55] [ERROR] 处理完成，登录失败 1 个账户
[2025-08-03 09:45:55] [DEBUG] 调试：成功账户邮箱列表 []
[2025-08-03 09:45:55] [DEBUG] 调试：失败账户邮箱列表 ['<EMAIL>']
[2025-08-03 09:45:55] [INFO] 文件更新完成：成功账户 0，失败账户 1，未处理账户 9
[2025-08-03 09:45:55] [DEBUG] 调试信息：成功账户列表长度 0
[2025-08-03 09:45:55] [INFO] 已更新文件内容
[2025-08-03 09:45:55] [INFO] 更新剩余账户数量: 9
[2025-08-03 09:45:56] [INFO] 尝试重新登录账户: <EMAIL>
[2025-08-03 09:45:57] [INFO] 已从失败账户列表中移除: <EMAIL>
[2025-08-03 09:45:57] [INFO] 开始重新登录账户: <EMAIL>
.com
[2025-08-03 09:46:01] [SUCCESS] 账户 <EMAIL> 重新登录成功
[2025-08-03 09:46:01] [SUCCESS] 账户 <EMAIL> 重新登录成功: 登录成功
[2025-08-03 09:46:02] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>']
[2025-08-03 09:46:02] [DEBUG] 调试：失败账户邮箱列表 []
[2025-08-03 09:46:02] [INFO] 将重新登录成功的账户从失败列表移到成功列表: <EMAIL>
[2025-08-03 09:46:02] [INFO] 文件更新完成：成功账户 1，失败账户 0，未处理账户 9
[2025-08-03 09:46:02] [DEBUG] 调试信息：成功账户列表长度 1
[2025-08-03 09:46:02] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', '0Lpk3a8B9', 'M.C537_BAY.0.U.-CmEfNuD!EfjS8SZmgvRpIfdAXtnRzP1XXBBhAgI8m2oPQUCXsp4uye!UI3bZJT0O3ufNJlTHeVa*COyLjx7n7p2xKe8lCCzn0DR32fsmEr5CcZsQauar2Q*U!Z7BeFdo*ik0vdd4CWzAMzZrNDJHOiB*sBGHF6AkCrQxObeNa0yHkT4XLGSoMRPoaVrj4KUMPO87sYugWR0ydUvzOi1Q1SUX!cpziaw8CQXbDRSq2jP9wQ1d7ifSnp!i*33bYciSG4iaKvdFAtJyG4Yff8o2ikQtmD36rknqVzOErTli7ShdzsnycxWMZfOB5i6HijCYONidBHE7IjzZMe8cWcnqwB9bPbTdbMZqUV3XI5FMTznVpiytuXaceRM05lH47sSqfwhsECHkVpnsMcDtVGK0QhBxcZ2m2VGbil0KEP3ZbRq3ap7CdEjPc70Vbu9mz9c*UQ$$', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-08-03 09:46:02] [INFO] 已更新文件内容
[2025-08-03 09:46:03] [DEBUG] 延迟清理重新登录线程引用: 行0
[2025-08-03 09:47:45] [INFO] AWS文件监控已停止
[2025-08-03 09:47:45] [INFO] 应用程序关闭
[2025-08-03 11:48:04] [INFO] 设置Qt插件路径: D:\Python311\Lib\site-packages\PyQt5\Qt5\plugins
[2025-08-03 11:48:04] [INFO] === AWS验证码获取工具启动 ===
[2025-08-03 11:48:04] [INFO] Python版本: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-08-03 11:48:04] [INFO] 系统平台: win32
[2025-08-03 11:48:04] [INFO] 已设置Windows任务栏图标 AppID: aws.verification.tool.1.0
[2025-08-03 11:48:04] [INFO] AWS文件监控已启动，监控当前目录
[2025-08-03 11:48:05] [INFO] 软件已初始化，请按照上方指南操作
[2025-08-03 11:48:10] [INFO] 更新剩余账户数量: 1
[2025-08-03 11:48:10] [INFO] 已选择文件: C:/Users/<USER>/Desktop/2025-08-02-以色列 - mail.txt
[2025-08-03 11:48:11] [INFO] 未指定账户数量，将加载全部账户
[2025-08-03 11:48:11] [INFO] 解析结果: 未使用账户 1个, 成功账户 5个, 失败账户 0个
[2025-08-03 11:48:11] [INFO] 从文件中解析出 1 个账户
[2025-08-03 11:48:11] [DEBUG] 已排除 5 个成功登录的账户
[2025-08-03 11:48:11] [DEBUG] 已排除 0 个登录失败的账户
[2025-08-03 11:48:11] [DEBUG] 筛选后可用账户数量: 1
[2025-08-03 11:48:12] [WARNING] 请求的账户数量(999999)超过可用账户数量(1)
[2025-08-03 11:48:12] [INFO] 开始处理 1 个账户
[2025-08-03 11:48:12] [INFO] 账户 <EMAIL> 状态: 登录中
[2025-08-03 11:48:17] [SUCCESS] 账户 <EMAIL> 登录成功
[2025-08-03 11:48:17] [SUCCESS] 账户 <EMAIL> 已标记为登录成功
[2025-08-03 11:48:17] [INFO] 账户 <EMAIL> 状态: 登录成功
[2025-08-03 11:48:17] [SUCCESS] 处理完成，成功登录 1 个账户
[2025-08-03 11:48:17] [DEBUG] 调试：成功账户邮箱列表 ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
[2025-08-03 11:48:17] [DEBUG] 调试：失败账户邮箱列表 []
[2025-08-03 11:48:17] [INFO] 文件更新完成：成功账户 6，失败账户 0，未处理账户 0
[2025-08-03 11:48:17] [DEBUG] 调试信息：成功账户列表长度 6
[2025-08-03 11:48:17] [DEBUG] 调试信息：第一个成功账户 ['<EMAIL>', 'qTg72r7iIPaO', 'M.C550_BL2.0.U.-ChTxPixS2G65XMnyb0wdRWRGYtXJuVYVg3envozD9QAkp8TFLu7Rzf8W8xKivqfSaKTMAgvtm3*xGsMt*B4VedefCWbqdQ8*I!6qDyydSCFH1YoBKBw6ciy6l9sI!1GDMGEBguLXY*nq9*w5qj9lXFCD7G!YwC1dUHxb4vMi05*HE1f2z2NK1KpL9wdCWyvZBDrWmwjdfkymqf09kp4Y!YhX8TZfv0o0Rw3y7aRGbvO10k9Q**LHK8HcPnm!nDEOT28MgMNe268luis5nVOOkTyo3JQt2nJUTnewsYLOBBuPPfqnnaP5PpttoI5qEyps56t2QvbsgqNQWAMLmLlg1EF5WNVN9UcuQ8NoaROd*ydhiNCamobWogewA*NULfbqm9kZdio85osh4YXGtveSTmXt!G3zv!*wxLTIzGIftWa0gklPB*uIDb*nGCAY*iPVTw$$', '9e5f94bc-e8a4-4e73-b8be-63364c29d753']
[2025-08-03 11:48:17] [INFO] 已更新文件内容
[2025-08-03 11:48:17] [INFO] 更新剩余账户数量: 0
[2025-08-03 11:50:08] [INFO] 尝试为 <EMAIL> 获取验证码
[2025-08-03 11:50:08] [DEBUG] 已禁用行0的获取验证码按钮
[2025-08-03 11:50:08] [DEBUG] 初始化验证码获取器: <EMAIL> (行0)
[2025-08-03 11:50:08] [INFO] 成功启动验证码获取线程: <EMAIL>
[2025-08-03 11:50:08] [DEBUG] 正在为 <EMAIL> 获取访问令牌
[2025-08-03 11:50:09] [DEBUG] 开始为 <EMAIL> 获取验证码
[2025-08-03 11:50:17] [DEBUG] 验证码获取结果: 062718
[2025-08-03 11:50:17] [SUCCESS] 验证码获取成功: 062718
[2025-08-03 11:50:17] [DEBUG] 行0无对应的AWS请求，跳过立即响应
[2025-08-03 11:50:17] [DEBUG] 更新验证码结果: 行0, 状态验证码获取成功, 验证码062718
[2025-08-03 11:50:17] [INFO] 验证码 062718 已复制到剪贴板
[2025-08-03 11:50:17] [DEBUG] 已恢复行0的按钮状态
[2025-08-03 11:50:17] [INFO] 行 1: 验证码获取成功, 验证码: 062718
[2025-08-03 11:50:17] [DEBUG] 检查AWS请求响应: aws_requests=[], row=0
[2025-08-03 11:50:17] [DEBUG] 验证码结果详情: status='验证码获取成功', code='062718', is_valid_code=True
[2025-08-03 11:50:17] [DEBUG] 行0无对应的AWS请求
[2025-08-03 11:50:17] [INFO] 验证码获取成功但无AWS请求，检查响应文件需求
[2025-08-03 11:50:17] [DEBUG] Windows系统，使用普通写入响应文件
[2025-08-03 11:50:17] [DEBUG] 已写入响应(单线程模式): Success|062718|1754193017225
[2025-08-03 11:50:17] [INFO] 验证码获取成功，创建新的响应文件: 062718
[2025-08-03 11:50:17] [INFO] 验证码获取成功，停止后续处理
[2025-08-03 11:50:17] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-08-03 11:50:19] [DEBUG] 清理线程时检查状态出错，认为线程已结束: wrapped C/C++ object of type QThread has been deleted
[2025-08-03 11:50:19] [DEBUG] 延迟清理线程引用: 行0
[2025-08-03 11:55:37] [INFO] 尝试为 <EMAIL> 获取验证码
[2025-08-03 11:55:37] [DEBUG] 已禁用行0的获取验证码按钮
[2025-08-03 11:55:37] [DEBUG] 初始化验证码获取器: <EMAIL> (行0)
[2025-08-03 11:55:37] [INFO] 成功启动验证码获取线程: <EMAIL>
[2025-08-03 11:55:37] [DEBUG] 正在为 <EMAIL> 获取访问令牌
[2025-08-03 11:55:39] [DEBUG] 开始为 <EMAIL> 获取验证码
[2025-08-03 11:55:47] [DEBUG] 验证码获取结果: 062718
[2025-08-03 11:55:47] [SUCCESS] 验证码获取成功: 062718
[2025-08-03 11:55:47] [DEBUG] 行0无对应的AWS请求，跳过立即响应
[2025-08-03 11:55:47] [DEBUG] 更新验证码结果: 行0, 状态验证码获取成功, 验证码062718
[2025-08-03 11:55:47] [INFO] 验证码 062718 已复制到剪贴板
[2025-08-03 11:55:47] [DEBUG] 已恢复行0的按钮状态
[2025-08-03 11:55:47] [INFO] 行 1: 验证码获取成功, 验证码: 062718
[2025-08-03 11:55:47] [DEBUG] 检查AWS请求响应: aws_requests=[], row=0
[2025-08-03 11:55:47] [DEBUG] 验证码结果详情: status='验证码获取成功', code='062718', is_valid_code=True
[2025-08-03 11:55:47] [DEBUG] 行0无对应的AWS请求
[2025-08-03 11:55:47] [INFO] 验证码获取成功但无AWS请求，检查响应文件需求
[2025-08-03 11:55:47] [INFO] 发现响应文件，当前内容: Success|062718|1754193017225
[2025-08-03 11:55:47] [DEBUG] Windows系统，使用普通写入响应文件
[2025-08-03 11:55:47] [DEBUG] 已写入响应(单线程模式): success|062718|1754193347411
[2025-08-03 11:55:47] [INFO] 手动获取验证码成功，已更新当前目录响应文件: 062718
[2025-08-03 11:55:47] [INFO] 验证码获取成功，停止后续处理
[2025-08-03 11:55:47] [DEBUG] 线程仍在运行，稍后清理: 行0
[2025-08-03 11:55:49] [DEBUG] 清理线程时检查状态出错，认为线程已结束: wrapped C/C++ object of type QThread has been deleted
[2025-08-03 11:55:49] [DEBUG] 延迟清理线程引用: 行0
