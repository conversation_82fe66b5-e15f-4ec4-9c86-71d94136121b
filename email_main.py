import os
import sys
import re
import time
import base64
import imaplib
import requests
import email
import email.utils
from email.header import decode_header
import threading
import json
import traceback  # 添加traceback模块用于详细错误报告
from datetime import datetime  # 添加datetime以获取更精确的时间戳
import ctypes  # 添加ctypes用于设置Windows任务栏图标
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QFileDialog, 
                             QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView, 
                             QVBoxLayout, QHBoxLayout, QWidget, QLabel, QLineEdit, 
                             QMessageBox, QProgressBar, QSpinBox, QTextEdit, QFrame, QMenu, QAction)
from PyQt5.QtGui import QIcon, QIntValidator, QFont, QColor
from PyQt5.QtCore import Qt, pyqtSignal, QObject, QThread, QTimer, QThreadPool, QRunnable

# 添加日志文件
LOG_FILE = "aws_tool_log.txt"  # 修改为直接使用当前目录的日志文件

# AWS软件通信文件
REQUEST_FILE = "EmailCodeRequest.txt"
RESPONSE_FILE = "EmailCodeResponse.txt"

# 多线程模式支持
MAX_THREADS = 10  # 最大支持10个线程
MAX_CONCURRENT_OPERATIONS = 6  # 最大并发操作数（验证码获取+重新登录）
MAX_NETWORK_CONNECTIONS = 10  # 最大网络连接数

# 支持嵌入式Python环境
def setup_embedded_python():
    # 获取当前运行路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查是否存在嵌入式Python环境
    python_dir = os.path.join(current_dir, 'python')
    if os.path.exists(python_dir):
        # 将嵌入式Python环境添加到PATH
        if 'PATH' in os.environ:
            os.environ['PATH'] = python_dir + os.pathsep + os.environ['PATH']
        else:
            os.environ['PATH'] = python_dir
        
        # 添加site-packages目录
        site_packages = os.path.join(python_dir, 'Lib', 'site-packages')
        if os.path.exists(site_packages) and site_packages not in sys.path:
            sys.path.insert(0, site_packages)

# 初始化嵌入式Python环境
setup_embedded_python()

def write_log(message, level="INFO"):
    """写入日志到文件"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}\n"
        
        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(log_message)
        except Exception as e:
            print(f"写入日志文件失败: {str(e)}")
        
        # 在控制台也输出日志
        print(f"[{level}] {message}")
    except Exception as e:
        print(f"写入日志时出错: {str(e)}")

# 获取应用程序图标的绝对路径
def get_icon_path():
    # 获取当前运行路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 首先尝试PNG图标
    png_path = os.path.join(current_dir, 'aws_icon.ico')
    if os.path.exists(png_path):
        return png_path
    
    # 如果PNG不存在，尝试ICO图标
    ico_path = os.path.join(current_dir, 'aws_icon.ico')
    if os.path.exists(ico_path):
        return ico_path
    
    # 如果都不存在，返回None
    return None

# 设置Windows任务栏图标
def set_windows_taskbar_icon():
    if sys.platform == 'win32':
        try:
            myappid = 'aws.verification.tool.1.0'  # 任意字符串，作为应用程序ID
            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
            write_log(f"已设置Windows任务栏图标 AppID: {myappid}", "INFO")
        except Exception as e:
            write_log(f"设置Windows任务栏图标失败: {str(e)}", "ERROR")

def write_log(message, level="INFO"):
    """写入日志到文件"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}\n"

        try:
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(log_message)
        except Exception as e:
            print(f"写入日志文件失败: {str(e)}")

        # 在控制台也输出日志
        print(f"[{level}] {message}")
    except Exception as e:
        print(f"写入日志时出错: {str(e)}")

# 线程池管理器
class ThreadPoolManager:
    """线程池管理器，用于控制并发数量和资源管理"""

    def __init__(self):
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(MAX_CONCURRENT_OPERATIONS)
        self.active_tasks = {}  # 跟踪活动任务
        self.task_counter = 0
        write_log(f"线程池初始化完成，最大并发数: {MAX_CONCURRENT_OPERATIONS}", "INFO")

    def submit_task(self, task, task_id=None):
        """提交任务到线程池"""
        if task_id is None:
            self.task_counter += 1
            task_id = f"task_{self.task_counter}"

        if self.thread_pool.activeThreadCount() >= MAX_CONCURRENT_OPERATIONS:
            write_log(f"线程池已满，等待空闲线程...", "WARNING")

        self.active_tasks[task_id] = task
        self.thread_pool.start(task)
        write_log(f"任务 {task_id} 已提交到线程池，当前活动线程: {self.thread_pool.activeThreadCount()}", "DEBUG")
        return task_id

    def remove_task(self, task_id):
        """从活动任务中移除已完成的任务"""
        if task_id in self.active_tasks:
            del self.active_tasks[task_id]
            write_log(f"任务 {task_id} 已完成并移除，剩余活动线程: {self.thread_pool.activeThreadCount()}", "DEBUG")

    def get_active_count(self):
        """获取当前活动线程数"""
        return self.thread_pool.activeThreadCount()

    def wait_for_done(self, timeout_ms=30000):
        """等待所有任务完成"""
        return self.thread_pool.waitForDone(timeout_ms)

    def clear(self):
        """清理线程池"""
        self.thread_pool.clear()
        self.active_tasks.clear()
        write_log("线程池已清理", "INFO")

class FileWatcher(FileSystemEventHandler):
    """文件监控类，监控AWS软件的验证码请求"""

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.last_processed_time = 0
        self.last_processed_content = None

    def on_modified(self, event):
        """文件修改时的回调"""
        if event.is_directory:
            return

        # 检查是否为请求文件（支持单线程和多线程模式）
        filename = os.path.basename(event.src_path)

        # 单线程模式
        if filename == REQUEST_FILE:
            # 避免重复处理同一个请求
            current_time = time.time()
            if current_time - self.last_processed_time < 2:  # 增加到2秒内不重复处理
                return
            self.last_processed_time = current_time

            try:
                # 读取文件内容，检查是否与上次处理的内容相同
                if os.path.exists(event.src_path):
                    with open(event.src_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                    
                    # 检查是否与上次处理的内容相同
                    if hasattr(self, 'last_processed_content') and self.last_processed_content == content:
                        write_log(f"跳过重复的请求内容: {content[:50]}...", "DEBUG")
                        return
                    
                    self.last_processed_content = content
                    self.process_request(event.src_path)
            except Exception as e:
                write_log(f"处理AWS请求时出错: {str(e)}", "ERROR")

        # 多线程模式
        elif filename.startswith("EmailCodeRequest_Thread") and filename.endswith(".txt"):
            try:
                self.process_request(event.src_path)
            except Exception as e:
                write_log(f"处理多线程AWS请求时出错: {str(e)}", "ERROR")

    def process_request(self, request_file_path):
        """处理AWS软件的验证码请求（支持单线程和多线程模式）"""
        try:
            # 读取请求文件
            if not os.path.exists(request_file_path):
                return

            with open(request_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                return

            # 确定是单线程还是多线程模式
            filename = os.path.basename(request_file_path)
            is_multithread = filename.startswith("EmailCodeRequest_Thread")
            thread_id = 0

            if is_multithread:
                # 从文件名提取线程ID：EmailCodeRequest_Thread1.txt -> 1
                try:
                    thread_id = int(filename.replace("EmailCodeRequest_Thread", "").replace(".txt", ""))
                    write_log(f"检测到多线程模式请求，线程ID: {thread_id}", "INFO")
                except:
                    write_log(f"无法解析线程ID，文件名: {filename}", "ERROR")
                    return

            # 解析请求内容
            if is_multithread:
                # 多线程模式：ThreadId:1|Email:<EMAIL>|Time:2024-01-01 12:00:00
                if not content.startswith(f"ThreadId:{thread_id}|"):
                    write_log(f"多线程请求格式错误或线程ID不匹配: {content}", "ERROR")
                    return

                parts = content.split('|')
                if len(parts) != 3:
                    write_log(f"多线程请求格式错误: {content}", "ERROR")
                    return

                email_part = parts[1]
                if not email_part.startswith("Email:"):
                    write_log(f"多线程请求邮箱格式错误: {content}", "ERROR")
                    return

                email_address = email_part[6:]  # 去掉"Email:"前缀
                request_type = "autoGetCode"  # 多线程模式默认自动获取
            else:
                # 单线程模式：getCode|<EMAIL>|timestamp 或 autoGetCode|<EMAIL>|timestamp
                parts = content.split('|')
                if len(parts) != 3 or (parts[0] != 'getCode' and parts[0] != 'autoGetCode'):
                    write_log(f"单线程请求格式错误: {content}", "ERROR")
                    return

                request_type = parts[0]
                email_address = parts[1]

            write_log(f"收到AWS验证码请求: {email_address} (线程ID: {thread_id})", "INFO")

            # 在主线程中处理请求
            self.main_window.handle_aws_request(email_address, content, thread_id, is_multithread)

        except Exception as e:
            write_log(f"处理请求文件时出错: {str(e)}", "ERROR")

class EmailWorker(QThread):
    """处理电子邮件操作的工作线程"""
    progress_updated = pyqtSignal(int, str, str)  # 进度, 状态, 验证码
    finish_signal = pyqtSignal(list)  # 成功登录的账户列表
    
    def __init__(self, accounts, count):
        super().__init__()
        self.accounts = accounts
        self.count = min(count, len(accounts))
        self.stop_flag = False
        self.pause_flag = False  # 添加暂停标志
        self.current_index = 0  # 当前处理的账户索引
        self.successful_accounts = []  # 成功登录的账户
        self.last_progress_time = time.time()  # 上次进度更新时间
        self.timeout_seconds = 15  # 超时秒数
        
    def run(self):
        try:
            i = self.current_index  # 从保存的索引开始处理
            
            while i < self.count:
                if self.stop_flag:
                    break
                    
                # 检查是否需要暂停处理
                while self.pause_flag and not self.stop_flag:
                    # 每0.5秒检查一次状态标志
                    write_log(f"线程暂停中，等待恢复信号...", "DEBUG")
                    time.sleep(0.5)
                    continue
                
                # 如果刚恢复，记录恢复状态
                if getattr(self, '_was_paused', False):
                    write_log(f"线程已经从暂停状态恢复，继续处理账户: {self.current_index}", "INFO")
                    self._was_paused = False
                
                account = self.accounts[i]
                email_address = account[0]
                password = account[1]
                refresh_token = account[2]
                client_id = account[3]
                
                # 更新UI显示处理状态并记录当前时间
                self.progress_updated.emit(i, "登录中", "")
                self.last_progress_time = time.time()
                
                # 创建超时检查线程
                self._setup_timeout_check(i, email_address)
                
                try:
                    # 获取访问令牌
                    access_token = self.get_access_token(client_id, refresh_token)
                    if not access_token:
                        self.progress_updated.emit(i, "登录失败", "")
                        write_log(f"账户 {email_address} 获取访问令牌失败", "ERROR")
                        i += 1  # 处理下一个账户
                        self.current_index = i  # 更新当前索引
                        self.last_progress_time = time.time()  # 重置进度时间
                        continue
                    
                    # 验证可以登录邮箱
                    if not self.verify_login(email_address, access_token):
                        self.progress_updated.emit(i, "登录失败", "")
                        write_log(f"账户 {email_address} 邮箱登录验证失败", "ERROR")
                        i += 1  # 处理下一个账户
                        self.current_index = i  # 更新当前索引
                        self.last_progress_time = time.time()  # 重置进度时间
                        continue
                    
                    # 登录成功
                    self.progress_updated.emit(i, "登录成功", "")
                    self.successful_accounts.append(account)
                    write_log(f"账户 {email_address} 登录成功", "SUCCESS")
                    i += 1  # 处理下一个账户
                    self.current_index = i  # 更新当前索引
                    self.last_progress_time = time.time()  # 重置进度时间
                except Exception as e:
                    self.progress_updated.emit(i, "登录失败", "")
                    write_log(f"账户 {email_address} 处理时出错: {str(e)}", "ERROR")
                    # 记录详细错误信息到日志文件
                    with open(LOG_FILE, "a", encoding="utf-8") as f:
                        f.write(f"错误详情: {traceback.format_exc()}\n")
                    i += 1  # 处理下一个账户
                    self.current_index = i  # 更新当前索引
                    self.last_progress_time = time.time()  # 重置进度时间
            
            # 完成后发送信号
            self.finish_signal.emit(self.successful_accounts)
        except Exception as e:
            write_log(f"工作线程运行时出错: {str(e)}", "ERROR")
            # 记录详细错误信息到日志文件
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"错误详情: {traceback.format_exc()}\n")
    
    def _setup_timeout_check(self, index, email_address):
        """设置超时检查线程"""
        def check_timeout():
            while not self.stop_flag and self.current_index == index:
                current_time = time.time()
                elapsed = current_time - self.last_progress_time
                
                if elapsed > self.timeout_seconds:
                    write_log(f"账户 {email_address} 处理超时 ({self.timeout_seconds}秒无响应)", "WARNING")
                    self.progress_updated.emit(index, "登录超时", "")
                    self.current_index += 1  # 移动到下一个账户
                    break
                    
                time.sleep(0.5)  # 每0.5秒检查一次
        
        # 创建并启动超时检查线程
        timeout_thread = threading.Thread(target=check_timeout)
        timeout_thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
        timeout_thread.start()
    
    def stop(self):
        write_log("停止登录处理线程", "INFO")
        self.stop_flag = True
        
    def pause(self):
        """暂停线程处理"""
        write_log("暂停登录处理线程", "INFO")
        write_log(f"当前处理到账户索引: {self.current_index}", "DEBUG")
        self.pause_flag = True
        self._was_paused = True  # 标记已暂停，便于恢复时检测
        
    def resume(self):
        """恢复线程处理"""
        write_log("恢复登录处理线程", "INFO")
        write_log(f"当前线程暂停状态: {self.pause_flag}", "DEBUG")
        # 确保线程真正恢复
        self.pause_flag = False
        write_log(f"设置pause_flag为: {self.pause_flag}", "DEBUG")
        # 记录恢复位置
        write_log(f"从账户索引 {self.current_index} 恢复处理", "INFO")
        # 重置进度时间
        self.last_progress_time = time.time()
    
    def get_access_token(self, client_id, refresh_token):
        """获取Microsoft OAuth2访问令牌，参考test.py实现"""
        try:
            data = {
                'client_id': client_id,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            # 设置5秒超时，减少长时间等待
            response = requests.post('https://login.live.com/oauth20_token.srf', data=data, timeout=20)
            
            # 记录返回内容到控制台，便于调试
            print(f"OAuth响应: {response.text}")
            self.last_progress_time = time.time()  # 更新进度时间
            
            if response.status_code == 200:
                try:
                    token = response.json()['access_token']
                    print(f"获取到访问令牌: {token[:10]}...{token[-10:]}")
                    return token
                except Exception as e:
                    print(f"解析访问令牌失败: {str(e)}")
                    return None
            else:
                print(f"获取令牌失败，状态码: {response.status_code}")
                return None
        except Exception as e:
            print(f"获取访问令牌时出错: {str(e)}")
            return None
    
    def verify_login(self, email_address, access_token):
        """验证是否可以登录到邮箱，参考test.py实现"""
        try:
            # 生成认证字符串
            auth_string = self.generate_auth_string(email_address, access_token)
            print(f"尝试使用OAuth2认证登录: {email_address}")
            
            # 使用IMAP协议验证登录，设置10秒超时避免长时间等待
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=10)
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            self.last_progress_time = time.time()  # 更新进度时间
            
            mail.select("INBOX")
            mail.close()
            mail.logout()
            
            print(f"邮箱 {email_address} 登录成功")
            return True
        except Exception as e:
            print(f"邮箱 {email_address} 登录失败: {str(e)}")
            return False
    
    def generate_auth_string(self, user, token):
        """生成OAuth2认证字符串，直接使用test.py的实现"""
        auth_string = f"user={user}\1auth=Bearer {token}\1\1"
        return auth_string
    
    def decode_str(self, s):
        """解码邮件主题"""
        if s:
            value, charset = decode_header(s)[0]
            if charset:
                if isinstance(value, bytes):
                    return value.decode(charset)
            return value
        return ""
    
    def get_aws_verification_code(self, email_address, access_token, wait_time=0):
        """获取AWS验证码，按照新需求：搜索24小时内收件箱和垃圾箱各3封邮件，筛选AWS邮件，返回最新验证码"""
        # 方案1：发件人 + 固定英文标识符识别
        aws_senders = ['signup.aws', 'verify.signin.aws', '<EMAIL>']
        fixed_identifiers = [
            'Amazon Web Services, Inc. es una filial de Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc.',
            'This message was created and distributed by Amazon.com, Inc.',
            'Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc. This message was created and distributed by Amazon Web Services, Inc.'
            '验证您的身份'
        ]
        patterns = [
            r'[\s>](\d{6})[\s<]',
            r'<strong>(\d{6})</strong>',
            r'<b>(\d{6})</b>',
            r'<span[^>]*>(\d{6})</span>',
            r'(?:código|code|verification|验证码|verificación).*?(\d{6})',
            r'(\d{6}).*?(?:código|code|verification|验证码|verificación)',
        ]
        mail = None
        try:
            print(f"正在连接IMAP服务器查找验证码邮件...")

            # 连接到IMAP服务器，设置15秒超时
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=15)
            auth_string = self.generate_auth_string(email_address, access_token)

            print(f"正在进行IMAP OAuth2认证...")
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            print(f"IMAP认证成功")

            # 需要搜索的文件夹列表：收件箱和垃圾邮件箱
            folders_to_search = ["INBOX", "Junk"]

            # 存储所有找到的AWS验证码邮件，按时间排序
            all_aws_emails = []

            # 计算24小时前的时间
            yesterday = time.strftime("%d-%b-%Y", time.gmtime(time.time() - 24*60*60))

            # 遍历每个文件夹
            for folder in folders_to_search:
                if self.stop_flag:
                    break

                try:
                    print(f"正在搜索文件夹: {folder}")
                    mail.select(folder)

                    # 搜索24小时内的邮件
                    status, messages = mail.search(None, f'SINCE "{yesterday}"')
                    if messages[0]:
                        email_ids = messages[0].split()
                        # 只取最新的3封邮件
                        latest_emails = email_ids[-3:] if len(email_ids) > 4 else email_ids
                        print(f"在{folder}中找到 {len(email_ids)} 封24小时内的邮件，检查最新的 {len(latest_emails)} 封")

                        # 检查每封邮件
                        for email_id in reversed(latest_emails):  # 从最新的开始检查
                            if self.stop_flag:
                                break

                            try:
                                status, msg_data = mail.fetch(email_id, '(RFC822)')
                                self.last_progress_time = time.time()

                                # 解析邮件内容
                                raw_email = msg_data[0][1]
                                msg = email.message_from_bytes(raw_email)

                                subject = self.decode_str(msg["Subject"])
                                sender = self.decode_str(msg.get("From", ""))
                                date_str = msg.get("Date", "")

                                print(f"检查邮件: {subject} (来自: {sender})")

                                # 检查是否是目标AWS邮件
                                is_target_email = False
                                email_type = None

                                # 方案1：发件人 + 固定英文标识符识别
                                sender_lower = sender.lower()
                                is_aws_sender = any(aws_sender in sender_lower for aws_sender in aws_senders)
                                if is_aws_sender:
                                    print(f"  发现AWS发件人: {sender}")
                                    # 提取邮件正文用于进一步检查
                                    email_body_text = ""
                                    if msg.is_multipart():
                                        for part in msg.walk():
                                            content_type = part.get_content_type()
                                            if content_type == "text/plain" or content_type == "text/html":
                                                try:
                                                    body = part.get_payload(decode=True)
                                                    if body:
                                                        try:
                                                            body_text = body.decode('utf-8')
                                                        except:
                                                            try:
                                                                body_text = body.decode('gbk')
                                                            except:
                                                                body_text = body.decode('utf-8', errors='ignore')
                                                        email_body_text += body_text + " "
                                                except Exception as e:
                                                    print(f"解析邮件部分时出错: {str(e)}")
                                                    continue
                                    else:
                                        try:
                                            body = msg.get_payload(decode=True)
                                            if body:
                                                try:
                                                    email_body_text = body.decode('utf-8')
                                                except:
                                                    try:
                                                        email_body_text = body.decode('gbk')
                                                    except:
                                                        email_body_text = body.decode('utf-8', errors='ignore')
                                        except Exception as e:
                                            print(f"解析邮件内容时出错: {str(e)}")
                                    has_fixed_identifier = any(identifier in email_body_text for identifier in fixed_identifiers)
                                    if has_fixed_identifier:
                                        print(f"  匹配AWS固定标识符")
                                        is_target_email = True
                                        if "指定的电子邮件地址已与某个 AWS 账户相关联" in email_body_text:
                                            email_type = "already_registered"
                                            verification_code = "已被注册"
                                            print(f"  确认为已注册邮件")
                                        else:
                                            email_type = "verification_code"
                                            print(f"  确认为验证码邮件")
                                    else:
                                        print(f"  未找到AWS固定标识符，跳过")
                                        continue
                                else:
                                    print(f"  非AWS发件人，跳过")
                                    continue

                                print(f"发现目标邮件: {subject}")

                                # 根据邮件类型处理
                                if email_type == "already_registered":
                                    verification_code = "已被注册"
                                    print(f"  确认为已注册邮件")
                                elif email_type == "verification_code":
                                    # 查找验证码
                                    verification_code = None
                                    for pattern in patterns:
                                        code_match = re.search(pattern, email_body_text, re.IGNORECASE)
                                        if code_match:
                                            verification_code = code_match.group(1)
                                            print(f"  找到验证码: {verification_code}")
                                            break

                                # 处理找到的结果
                                if verification_code:
                                    # 解析邮件时间
                                    try:
                                        email_time = email.utils.parsedate_to_datetime(date_str)
                                        timestamp = email_time.timestamp()
                                    except:
                                        timestamp = time.time()  # 如果解析失败，使用当前时间

                                    if email_type == "already_registered":
                                        # 已注册邮件，直接记录
                                        all_aws_emails.append({
                                            'type': 'already_registered',
                                            'code': verification_code,
                                            'timestamp': timestamp,
                                            'subject': subject,
                                            'folder': folder,
                                            'sender': sender
                                        })
                                        print(f"在{folder}中找到已注册邮件: {subject}")
                                    elif email_type == "verification_code":
                                        # 验证码邮件，需要验证验证码有效性
                                        if (len(verification_code) == 6 and
                                            verification_code.isdigit() and
                                            verification_code != "000000" and
                                            verification_code != "010001" and
                                            len(set(verification_code)) > 1):  # 不能是重复数字

                                            all_aws_emails.append({
                                                'type': 'verification_code',
                                                'code': verification_code,
                                                'timestamp': timestamp,
                                                'subject': subject,
                                                'folder': folder,
                                                'sender': sender
                                            })
                                            print(f"在{folder}中找到有效AWS验证码: {verification_code} (邮件: {subject})")
                                        else:
                                            print(f"  跳过无效验证码: {verification_code}")

                            except Exception as e:
                                print(f"处理邮件时出错: {str(e)}")
                                continue
                    else:
                        print(f"在{folder}中未找到24小时内的邮件")

                except Exception as e:
                    print(f"搜索文件夹 {folder} 时出错: {str(e)}")
                    continue

            # 关闭IMAP连接
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass

            # 处理找到的邮件
            if all_aws_emails:
                # 按时间戳排序，最新的在最后
                all_aws_emails.sort(key=lambda x: x['timestamp'])
                latest_email = all_aws_emails[-1]

                print(f"找到 {len(all_aws_emails)} 个目标邮件，最新的:")
                print(f"类型: {latest_email.get('type', 'unknown')}")
                print(f"结果: {latest_email['code']}")
                print(f"来源: {latest_email['folder']} - {latest_email['subject']}")
                print(f"发件人: {latest_email['sender']}")

                # 按优先级返回结果
                if latest_email.get('type') == 'already_registered':
                    return "已被注册"
                elif latest_email.get('type') == 'verification_code':
                    return latest_email['code']
                else:
                    # 如果最新的不是目标邮件，但有验证码邮件，返回未获取验证码
                    print("最新邮件不是目标类型")
                    return None
            else:
                print("在收件箱和垃圾邮件箱的24小时内邮件中都未找到目标邮件")
                return None

        except Exception as e:
            print(f"获取验证码时出错: {str(e)}")
            # 确保IMAP连接被正确关闭
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass
            return None


class AWSVerificationTool(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 设置应用图标
        icon_path = get_icon_path()
        if icon_path:
            self.app_icon = QIcon(icon_path)
            self.setWindowIcon(self.app_icon)
            write_log(f"已设置应用图标: {icon_path}", "INFO")
        
        self.setWindowTitle('AWS验证码获取工具')
        self.center_window()
        self.initUI()
        self.show_initial_help()
        #write_log("软件已初始化，请按照上方指南操作", "INFO")
        
        self.accounts = []  # 存储所有账户数据
        self.successful_accounts = []  # 存储成功登录的账户
        self.failed_accounts = []  # 存储登录失败的账户
        self.current_file = ""  # 当前选择的文件
        self.code_workers = {}  # 存储验证码获取线程（保留兼容性）
        self.retry_workers = {}  # 存储重新登录线程（保留兼容性）
        self.email_paused = False  # 是否暂停了邮件登录线程
        self.pending_accounts = []  # 存储因获取验证码而暂停处理的账户
        self.current_account_index = 0  # 当前处理的账户索引
        self.aws_requests = {}  # AWS请求信息字典 {row: request_info} 支持多线程并发

        # 新增线程池管理器（暂时禁用）
        # self.thread_pool_manager = ThreadPoolManager()
        # self.active_code_tasks = {}  # 跟踪验证码获取任务
        # self.active_login_tasks = {}  # 跟踪重新登录任务
        
        # 将窗口移动到屏幕中央
        self.center_window()

        # 启动文件监控
        self.setup_file_watcher()

    def setup_file_watcher(self):
        """设置文件监控"""
        try:
            # 创建文件监控器
            self.file_watcher = FileWatcher(self)
            self.observer = Observer()

            # 只监控当前邮箱软件运行的目录
            self.observer.schedule(self.file_watcher, path='.', recursive=False)
            write_log("AWS文件监控已启动，监控当前目录", "INFO")

            self.observer.start()
        except Exception as e:
            write_log(f"启动文件监控失败: {str(e)}", "ERROR")

    def handle_aws_request(self, target_email, request_content, thread_id=0, is_multithread=False):
        """处理AWS软件的验证码请求（支持单线程和多线程模式）"""
        try:
            mode_desc = f"多线程模式(线程{thread_id})" if is_multithread else "单线程模式"
            write_log(f"处理AWS验证码请求: {target_email} ({mode_desc})", "INFO")

            # 提取时间戳
            if is_multithread:
                # 多线程模式从request_content中提取时间戳
                parts = request_content.split('|')
                request_timestamp = parts[2].replace("Time:", "") if len(parts) > 2 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 单线程模式从request_content中提取时间戳
                parts = request_content.split('|')
                request_timestamp = parts[2] if len(parts) > 2 else datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 查找对应的邮箱账户
            target_row = -1
            for i in range(self.table.rowCount()):
                email_item = self.table.item(i, 0)
                if email_item and email_item.text() == target_email:
                    target_row = i
                    break

            if target_row == -1:
                # 邮箱不在当前表格中
                self.write_response("error", f"邮箱 {target_email} 未在登录列表中", request_timestamp, thread_id, is_multithread)
                write_log(f"邮箱 {target_email} 未在登录列表中", "WARNING")
                return

            # 检查邮箱登录状态
            status_item = self.table.item(target_row, 2)
            if not status_item or status_item.text() != "登录成功":
                # 检查是否是登录失败状态
                if status_item and status_item.text() == "登录失败":
                    self.write_response("NotFound", f"邮箱 {target_email} 登录失败", request_timestamp, thread_id, is_multithread)
                    write_log(f"邮箱 {target_email} 登录失败", "WARNING")
                else:
                    self.write_response("Error", f"邮箱 {target_email} 未登录成功", request_timestamp, thread_id, is_multithread)
                    write_log(f"邮箱 {target_email} 未登录成功，当前状态: {status_item.text() if status_item else '未知'}", "WARNING")
                return

            # 检查该账户是否已经有正在运行的验证码获取线程
            if target_row in self.code_workers:
                worker_data = self.code_workers[target_row]
                thread = worker_data.get('thread')
                
                # 安全检查线程状态，避免访问已删除的QThread对象
                thread_running = False
                try:
                    if thread and hasattr(thread, 'isRunning'):
                        thread_running = thread.isRunning()
                except Exception as e:
                    # 如果访问线程状态出错，认为线程已经结束
                    write_log(f"检查线程状态时出错，认为线程已结束: {str(e)}", "DEBUG")
                    thread_running = False
                    # 清理无效的线程引用
                    try:
                        self.code_workers.pop(target_row, None)
                    except:
                        pass
                
                if thread_running:
                    write_log(f"账户 {target_email} 已有验证码获取线程正在运行，忽略此次请求", "INFO")
                    return
            
            # 检查是否已经获取到验证码（避免重复获取）
            # 注意：这个检查对单线程和多线程都是有益的，不会影响正常流程
            # code_item = self.table.item(target_row, 3)
            # if code_item and code_item.text() and code_item.text().isdigit() and len(code_item.text()) == 6:
            #     write_log(f"账户 {target_email} 已经获取到验证码 {code_item.text()}，跳过重复获取 ({mode_desc})", "INFO")
            #     # 即使跳过重复获取，也要写入响应文件
            #     self.write_response("success", code_item.text(), request_timestamp, thread_id, is_multithread)
            #     write_log(f"已为现有验证码写入响应: {code_item.text()}", "INFO")
            #     return

            # 自动点击获取验证码按钮
            get_code_btn = self.table.cellWidget(target_row, 4)
            if get_code_btn and get_code_btn.isEnabled():
                write_log(f"自动点击 {target_email} 的获取验证码按钮", "INFO")

                # 保存请求时间戳，用于后续响应（支持多线程并发）
                self.aws_requests[target_row] = {
                    'email': target_email,
                    'timestamp': request_timestamp,
                    'row': target_row,
                    'thread_id': thread_id,
                    'is_multithread': is_multithread
                }
                write_log(f"保存AWS请求信息到行{target_row}: {self.aws_requests[target_row]}", "DEBUG")

                # 模拟点击按钮
                get_code_btn.click()
            else:
                self.write_response("Error", f"邮箱 {target_email} 获取验证码按钮不可用", request_timestamp, thread_id, is_multithread)
                write_log(f"邮箱 {target_email} 获取验证码按钮不可用", "WARNING")

        except Exception as e:
            self.write_response("Error", f"处理请求时出错: {str(e)}", request_timestamp, thread_id, is_multithread)
            write_log(f"处理AWS请求时出错: {str(e)}", "ERROR")



    def write_response(self, status, data, timestamp, thread_id=0, is_multithread=False):
        """写入响应文件（支持单线程和多线程模式）"""
        try:
            # 验证和清理数据，防止错误信息写入验证码位置
            cleaned_data = data
            if status in ["Success", "success"] and data:
                # 对于成功状态，验证data是否为有效验证码
                if not (isinstance(data, str) and len(data) == 6 and data.isdigit()):
                    # 如果不是6位数字验证码，记录警告并修正状态
                    write_log(f"警告：成功状态但验证码格式无效: {data}", "WARNING")
                    status = "error"
                    cleaned_data = f"验证码格式错误: {data}"
            elif status in ["Error", "error", "NotFound", "notfound"] and not data:
                # 对于错误状态，如果data为空，提供默认错误信息
                cleaned_data = "获取验证码失败"

            if is_multithread:
                # 多线程模式：ThreadId:1|Code:123456|Status:success|Time:2024-01-01 12:00:00
                response_content = f"ThreadId:{thread_id}|Code:{cleaned_data}|Status:{status.title()}|Time:{timestamp}"
                response_file = f"EmailCodeResponse_Thread{thread_id}.txt"
            else:
                # 单线程模式：status|data|timestamp
                response_content = f"{status}|{cleaned_data}|{timestamp}"
                response_file = RESPONSE_FILE

            # 响应文件写入当前目录（与邮箱软件同目录）

            # 使用文件锁防止多线程同时写入（Windows兼容）
            try:
                import fcntl
                # Unix/Linux系统使用fcntl文件锁
                with open(response_file, 'w', encoding='utf-8') as f:
                    fcntl.flock(f.fileno(), fcntl.LOCK_EX)  # 独占锁
                    f.write(response_content)
                    fcntl.flock(f.fileno(), fcntl.LOCK_UN)  # 释放锁
                write_log(f"使用fcntl文件锁写入响应文件", "DEBUG")
            except ImportError:
                # Windows系统不支持fcntl，使用普通写入
                with open(response_file, 'w', encoding='utf-8') as f:
                    f.write(response_content)
                write_log(f"Windows系统，使用普通写入响应文件", "DEBUG")

            mode_desc = f"多线程模式(线程{thread_id})" if is_multithread else "单线程模式"
            write_log(f"已写入响应({mode_desc}): {response_content}", "DEBUG")
        except Exception as e:
            write_log(f"写入响应文件失败: {str(e)}", "ERROR")

    def cleanup_thread_reference(self, row):
        """清理线程引用"""
        try:
            if row in self.code_workers:
                worker_data = self.code_workers[row]
                thread = worker_data.get('thread')

                # 安全检查线程状态，避免访问已删除的QThread对象
                thread_running = False
                try:
                    if thread and hasattr(thread, 'isRunning'):
                        thread_running = thread.isRunning()
                except Exception as e:
                    # 如果访问线程状态出错，认为线程已经结束
                    write_log(f"清理线程时检查状态出错，认为线程已结束: {str(e)}", "DEBUG")
                    thread_running = False

                if not thread_running:
                    write_log(f"延迟清理线程引用: 行{row}", "DEBUG")
                    try:
                        self.code_workers.pop(row, None)
                    except:
                        pass
                else:
                    write_log(f"线程仍在运行，无法清理: 行{row}", "WARNING")
        except Exception as e:
            write_log(f"延迟清理线程引用时出错: {str(e)}", "ERROR")
            # 即使出错也尝试清理
            try:
                if row in self.code_workers:
                    self.code_workers.pop(row, None)
            except:
                pass

    def center_window(self):
        """将窗口居中显示并设置为屏幕60%宽度和70%高度"""
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen().geometry()
        # 计算窗口尺寸为屏幕的60%宽度和70%高度
        window_width = int(screen.width() * 0.6)
        window_height = int(screen.height() * 0.7)  # 增加到70%高度
        # 设置窗口大小
        self.resize(window_width, window_height)
        # 计算中心位置
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 4
        # 移动窗口到中心位置
        self.move(x, y)
        
    def initUI(self):
        self.setWindowTitle('AWS验证码获取工具')
        
        # 安全加载图标并设为应用程序全局图标
        try:
            # 设置窗口图标
            icon_path = 'aws_icon.ico'
            if os.path.exists(icon_path):
                app_icon = QIcon(icon_path)
                self.setWindowIcon(app_icon)
                
                # 设置全局应用图标
                app = QApplication.instance()
                if app:
                    app.setWindowIcon(app_icon)
                    # 设置任务栏图标（仅适用于Windows）
                    if sys.platform == 'win32':
                        try:
                            import ctypes
                            myappid = 'aws.verification.tool.1.0'  # 任意字符串，作为应用程序ID
                            ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
                            write_log(f"已设置Windows任务栏图标 AppID: {myappid}", "INFO")
                        except Exception as e:
                            write_log(f"设置Windows任务栏图标失败: {str(e)}", "WARNING")
        except Exception as e:
            self.log_message(f"加载图标出错: {str(e)}", "ERROR")
        
        self.setGeometry(100, 100, 900, 600)  # 初始大小，之后会被center_window覆盖
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 上部控制区域 - 使用更好的间距和对齐
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(10, 10, 10, 10)
        control_layout.setSpacing(10)
        
        # 文件选择部分
        file_section = QHBoxLayout()
        self.select_file_btn = QPushButton('选择文件')
        self.select_file_btn.setMinimumWidth(100)
        self.select_file_btn.clicked.connect(self.select_file)
        file_section.addWidget(self.select_file_btn)
        
        # 文件名标签 - 添加边框和背景色使其更突出
        self.no_file_label = QLabel('未选择文件')
        self.no_file_label.setStyleSheet("""
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
        """)
        self.no_file_label.setMaximumWidth(580)  # 增加宽度以显示完整路径
        file_section.addWidget(self.no_file_label)
        
        control_layout.addLayout(file_section)
        
        # 添加弹性空间
        control_layout.addStretch(1)
        
        # 账户数量控制 - 添加组合布局使其更整齐
        count_section = QHBoxLayout()
        count_section.setSpacing(5)
        
        self.account_count_label = QLabel('账户数量:')
        count_section.addWidget(self.account_count_label)
        
        self.account_count_input = QLineEdit()
        self.account_count_input.setValidator(QIntValidator(1, 999999))
        self.account_count_input.setFixedWidth(80)
        self.account_count_input.setPlaceholderText("全部")
        count_section.addWidget(self.account_count_input)
        
        control_layout.addLayout(count_section)
        
        # 加载按钮
        self.load_btn = QPushButton('加载账户')
        self.load_btn.setMinimumWidth(100)
        control_layout.addWidget(self.load_btn)
        self.load_btn.clicked.connect(self.load_accounts)
        
        # 剩余账户显示 - 使用更突出的样式
        self.remaining_label = QLabel('剩余: 0')
        self.remaining_label.setStyleSheet("""
            background-color: #e7f3ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            padding: 5px 10px;
            color: #0078d7;
            font-weight: bold;
        """)
        control_layout.addWidget(self.remaining_label)
        
        main_layout.addLayout(control_layout)
        
        # 添加分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)  # 修改为5列，移除多余的数字列
        self.table.setHorizontalHeaderLabels(['邮箱', '密码', '状态', '验证码', '操作'])
        
        # 设置列宽比例
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 序号列自适应内容宽度
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 邮箱列可伸缩
        
        # 按照设定比例设置列宽
        self.table.setColumnWidth(0, int(self.width() * 0.30))  # 邮箱 30%
        self.table.setColumnWidth(1, int(self.width() * 0.20))  # 密码 20%
        self.table.setColumnWidth(2, int(self.width() * 0.15))  # 状态 15%
        self.table.setColumnWidth(3, int(self.width() * 0.15))  # 验证码 15%
        self.table.setColumnWidth(4, int(self.width() * 0.20))  # 操作 20%
        
        # 设置表格样式
        self.table.setAlternatingRowColors(True)  # 交替行颜色
        self.table.setSelectionBehavior(QTableWidget.SelectItems)  # 修改为选择单元格而不是整行
        self.table.setSelectionMode(QTableWidget.ExtendedSelection)  # 允许扩展选择(按住Shift或Ctrl多选)
        
        # 加深选中项的背景色
        self.table.setStyleSheet("""
            QTableWidget::item:selected {
                background-color: #2979ff;  /* 更深的蓝色 */
                color: white;  /* 白色文字，确保可见 */
            }
        """)
        
        # 连接键盘事件和右键菜单
        self.table.keyPressEvent = self.table_key_press
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)
        
        main_layout.addWidget(self.table)
        
        # 添加操作日志区域，改进样式和布局
        log_label = QLabel('操作日志:')
        log_label.setStyleSheet("font-weight: bold; margin-top: 5px;")
        main_layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 9pt;
            line-height: 1.3;
        """)
        main_layout.addWidget(self.log_text)
        
        # 设置中央窗口部件
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        # 设置剪贴板
        self.clipboard = QApplication.clipboard()
        
        # 设置应用程序整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                gridline-color: #e0e0e0;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e7f3ff;
                color: #000;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 6px;
                border: none;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                font-weight: bold;
            }
            QPushButton {
                background-color: #0078d7;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0063b1;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QLabel {
                color: #333;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
        """)
        
    def select_file(self):
        """选择TXT文件"""
        # 获取桌面路径作为默认目录
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        file_path, _ = QFileDialog.getOpenFileName(self, '选择文件', desktop_path, 'Text Files (*.txt)')
        if file_path:
            self.current_file = file_path
            self.no_file_label.setText(file_path)  # 显示完整路径
            self.update_remaining_count()
            self.log_message(f"已选择文件: {file_path}")
    
    def is_client_id(self, text):
        """判断文本是否为Client ID
        Client ID特征：由字母（a-z）、数字（0-9）及 4 个连字符（-）组成
        通常是UUID格式，如：9e5f94bc-e8a4-4e73-b8be-63364c29d753
        """
        if not text:
            return False

        # 计算连字符数量
        hyphen_count = text.count('-')
        if hyphen_count != 4:
            return False

        # 检查是否只包含字母、数字和连字符
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-')
        text_chars = set(text)

        # 如果包含不允许的字符，则不是Client ID
        if not text_chars.issubset(allowed_chars):
            return False

        # 确保不是纯连字符
        if text.replace('-', '') == '':
            return False

        # 检查是否符合UUID格式 (8-4-4-4-12)
        parts = text.split('-')
        if len(parts) == 5:
            expected_lengths = [8, 4, 4, 4, 12]
            for i, part in enumerate(parts):
                if len(part) != expected_lengths[i]:
                    return False
                # 检查每部分是否只包含十六进制字符
                if not all(c in '0123456789abcdefABCDEF' for c in part):
                    return False
            return True

        return True  # 其他符合基本条件的也认为是Client ID

    def parse_accounts(self, file_path):
        """解析账户文件，支持灵活格式
        支持格式：
        1. 邮箱----密码----刷新令牌----Client ID
        2. 邮箱----密码----Client ID----刷新令牌----辅助邮箱
        """
        accounts = []
        successful_accounts = []  # 存储已登录的账户
        failed_accounts = []      # 存储登录失败的账户
        processed_emails = set()  # 用于检查重复的邮箱
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                current_section = "unused"  # 标记当前正在读取的部分
                
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    # 检查是否进入特定部分
                    if line == "已被登录的账户：":
                        current_section = "success"
                        continue
                    elif line == "登录失败账户：":
                        current_section = "failed"
                        continue
                        
                    # 解析账户行
                    parts = line.split('----')
                    if len(parts) >= 4:
                        email = parts[0]
                        
                        # 如果邮箱已经处理过，跳过这一行
                        if email in processed_emails:
                            self.log_message(f"跳过重复的账户: {email}", "WARNING")
                            continue
                            
                        # 判断第三个字段是否为Client ID
                        if self.is_client_id(parts[2]):
                            # 格式2: 邮箱----密码----Client ID----刷新令牌----[辅助邮箱]
                            client_id = parts[2]
                            refresh_token = parts[3]
                        else:
                            # 格式1: 邮箱----密码----刷新令牌----Client ID
                            refresh_token = parts[2]
                            client_id = parts[3]
                            
                        # 构建账户数据
                        account = [email, parts[1], refresh_token, client_id]
                        
                        # 根据所在部分分类账户
                        if current_section == "success":
                            successful_accounts.append(account)
                        elif current_section == "failed":
                            failed_accounts.append(account)
                        else:
                            accounts.append(account)
                            
                        processed_emails.add(email)
                        
            # 更新类的账户列表
            self.successful_accounts = successful_accounts
            self.failed_accounts = failed_accounts
            
            # 输出调试信息
            self.log_message(f"解析结果: 未使用账户 {len(accounts)}个, 成功账户 {len(successful_accounts)}个, 失败账户 {len(failed_accounts)}个")
            
            return accounts
            
        except Exception as e:
            self.log_message(f"解析文件出错: {str(e)}", "ERROR")
            return []
    
    def load_accounts(self):
        """加载账户，增强错误处理"""
        try:
            if not self.current_file:
                QMessageBox.warning(self, '警告', '请先选择文件!')
                self.log_message("未选择文件，请先选择账户文件", "WARNING")
                return
                
            # 获取需要加载的账户数量
            count_text = self.account_count_input.text()
            if not count_text:
                # 如果未输入数量，则使用全部账户
                count_text = "999999"
                self.log_message("未指定账户数量，将加载全部账户")
            
            try:
                count = int(count_text)
                if count <= 0:
                    QMessageBox.warning(self, '警告', '账户数量必须大于0!')
                    self.log_message("指定的账户数量无效，必须大于0", "WARNING")
                    return
            except ValueError:
                QMessageBox.warning(self, '警告', '请输入有效的账户数量!')
                self.log_message("输入的账户数量无效", "ERROR")
                return
            
            # 解析文件获取账户
            self.accounts = self.parse_accounts(self.current_file)
            if not self.accounts:
                QMessageBox.warning(self, '警告', '未能从文件中解析出有效账户!')
                self.log_message("未能从文件中解析出有效账户", "ERROR")
                return
            
            self.log_message(f"从文件中解析出 {len(self.accounts)} 个账户")
            
            # 计算可用账户数量 - 修改后的逻辑
            used_emails = set()
            # 添加已成功登录的账户邮箱
            if hasattr(self, 'successful_accounts'):
                used_emails.update(acc[0] for acc in self.successful_accounts)
                self.log_message(f"已排除 {len(self.successful_accounts)} 个成功登录的账户", "DEBUG")
            # 添加登录失败的账户邮箱
            if hasattr(self, 'failed_accounts'):
                used_emails.update(acc[0] for acc in self.failed_accounts)
                self.log_message(f"已排除 {len(self.failed_accounts)} 个登录失败的账户", "DEBUG")
            
            # 筛选未使用的账户
            available_accounts = [acc for acc in self.accounts if acc[0] not in used_emails]
            self.log_message(f"筛选后可用账户数量: {len(available_accounts)}", "DEBUG")
            
            # 检查是否有足够的账户
            if count > len(available_accounts):
                QMessageBox.warning(self, '警告', f'该文本只有{len(available_accounts)}个可用账户!')
                self.log_message(f"请求的账户数量({count})超过可用账户数量({len(available_accounts)})", "WARNING")
                count = len(available_accounts)
            
            if count == 0:
                QMessageBox.warning(self, '警告', '没有可用账户!')
                self.log_message("没有可用账户", "WARNING")
                return
                
            # 清空表格
            self.table.setRowCount(0)
            
            # 填充表格
            self.table.setRowCount(count)
            for i in range(count):
                try:
                    # 邮箱
                    account = available_accounts[i]
                    email_item = QTableWidgetItem(account[0])
                    email_item.setFlags(email_item.flags() & ~Qt.ItemIsEditable)
                    email_item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(i, 0, email_item)
                    
                    # 密码 (明文显示)
                    password_item = QTableWidgetItem(account[1])
                    password_item.setFlags(password_item.flags() & ~Qt.ItemIsEditable)
                    password_item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(i, 1, password_item)
                    
                    # 状态 (初始状态设为"等待验证")
                    status_item = QTableWidgetItem('等待验证')
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(i, 2, status_item)
                    
                    # 验证码
                    code_item = QTableWidgetItem('')
                    code_item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(i, 3, code_item)
                    
                    # 操作按钮
                    get_code_btn = QPushButton('获取验证码')
                    get_code_btn.clicked.connect(lambda _, row=i: self.get_code_for_row(row))
                    # 初始状态下设置为禁用，直到登录成功后再启用
                    get_code_btn.setEnabled(False)
                    self.table.setCellWidget(i, 4, get_code_btn)
                except Exception as e:
                    self.log_message(f"设置表格行 {i+1} 时出错: {str(e)}", "ERROR")
                    # 记录详细错误
                    with open(LOG_FILE, "a", encoding="utf-8") as f:
                        f.write(f"错误详情: {traceback.format_exc()}\n")
            
            # 如果已经有邮件工作线程在运行，停止它
            if hasattr(self, 'email_worker') and self.email_worker.isRunning():
                self.email_worker.stop()
                self.email_worker.wait()
            
            # 重置状态标志
            self.email_paused = False
            
            # 启动工作线程处理账户登录验证
            try:
                self.email_worker = EmailWorker(available_accounts[:count], count)
                self.email_worker.progress_updated.connect(self.update_progress)
                self.email_worker.finish_signal.connect(self.process_finished)
                self.email_worker.start()
                
                self.log_message(f"开始处理 {count} 个账户", "INFO")
                self.load_btn.setEnabled(False)  # 禁用加载按钮，避免重复操作
                self.load_btn.setText("正在处理...")
            except Exception as e:
                self.log_message(f"启动工作线程时出错: {str(e)}", "ERROR")
                # 记录详细错误
                with open(LOG_FILE, "a", encoding="utf-8") as f:
                    f.write(f"错误详情: {traceback.format_exc()}\n")
        except Exception as e:
            self.log_message(f"加载账户时出错: {str(e)}", "ERROR")
            QMessageBox.critical(self, '错误', f'加载账户时发生错误: {str(e)}')
            # 记录详细错误
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"错误详情: {traceback.format_exc()}\n")
    
    def update_progress(self, index, status, code):
        """更新处理进度 - 使用更明显的状态颜色"""
        try:
            # 获取当前行的电子邮件地址
            email = self.table.item(index, 0).text()
            
            # 根据状态设置颜色和控件 - 使用更好的配色方案
            if status == "登录成功":
                # 使用文本项显示状态
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                # 设置为不可编辑
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                # 淡蓝色背景
                status_item.setBackground(QColor(173, 216, 230))  # 淡蓝色
                status_item.setForeground(QColor(0, 0, 139))  # 深蓝色文字
                
                # 记录登录成功的账户
                found_account = None
                for account in self.accounts:
                    if account[0] == email:
                        found_account = account
                        break
                        
                if found_account and found_account not in self.successful_accounts:
                    self.successful_accounts.append(found_account)
                    self.log_message(f"账户 {email} 已标记为登录成功", "SUCCESS")
                        
                # 启用获取验证码按钮
                get_code_btn = self.table.cellWidget(index, 4)
                if get_code_btn:
                    get_code_btn.setEnabled(True)
                
                self.table.setItem(index, 2, status_item)
                    
            elif status == "登录失败":
                # 使用按钮替代文本项，允许重新登录
                retry_btn = QPushButton("重新登录")
                retry_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #ffcccc;
                        color: #8b0000;
                        border: 1px solid #ff9999;
                        border-radius: 4px;
                        padding: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #ff9999;
                    }
                    QPushButton:pressed {
                        background-color: #ff6666;
                    }
                """)
                retry_btn.clicked.connect(lambda _, row=index: self.retry_login(row))
                self.table.setCellWidget(index, 2, retry_btn)
                
                # 记录登录失败的账户
                found_account = None
                for account in self.accounts:
                    if account[0] == email:
                        found_account = account
                        break
                        
                if found_account and found_account not in self.failed_accounts:
                    self.failed_accounts.append(found_account)
                    self.log_message(f"账户 {email} 已标记为登录失败", "ERROR")
                    
            elif status == "登录中":
                # 橘色背景的文本项
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                # 设置为不可编辑
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                status_item.setBackground(QColor(255, 200, 102))  # 橘色
                status_item.setForeground(QColor(139, 69, 0))  # 深橘色文字
                self.table.setItem(index, 2, status_item)
            else:
                # 其他状态使用普通文本项
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                # 设置为不可编辑
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(index, 2, status_item)
            
            # 记录详细日志信息
            self.log_message(f"账户 {email} 状态: {status}")
        except Exception as e:
            print(f"更新进度时出错: {str(e)}")
            self.log_message(f"更新进度时出错: {str(e)}", "ERROR")
    
    def get_code_for_row(self, row):
        """为特定行获取验证码，支持多线程并发，不阻塞登录进程"""
        try:
            if row < 0 or row >= self.table.rowCount():
                self.log_message(f"无效的行号: {row}", "ERROR")
                return

            # 获取当前行的账户信息
            email_item = self.table.item(row, 0)
            if not email_item:
                self.log_message(f"行 {row} 没有邮箱信息", "ERROR")
                return

            email = email_item.text()
            self.log_message(f"尝试为 {email} 获取验证码")

            # 检查是否已经获取到验证码（避免重复获取）
            # code_item = self.table.item(row, 3)
            # if code_item and code_item.text() and code_item.text().isdigit() and len(code_item.text()) == 6:
            #     self.log_message(f"账户 {email} 已经获取到验证码 {code_item.text()}，跳过重复获取", "INFO")
            #     return

            # 检查是否已经登录成功
            status_item = self.table.item(row, 2)
            if not status_item:
                self.log_message(f"行 {row} 没有状态信息", "ERROR")
                return

            status_text = status_item.text()

            # 即使正在登录中，也允许尝试获取验证码，但给予用户警告
            if status_text == "登录中":
                # 在自动模式下不显示弹窗
                if not hasattr(self, 'current_aws_request') or not self.current_aws_request:
                    QMessageBox.warning(self, '警告', f'账户 {email} 正在登录中，验证码获取可能失败!')
                self.log_message(f"警告: 账户 {email} 正在登录中，验证码获取可能不成功", "WARNING")
            elif status_text == "登录失败":
                # 在自动模式下不显示弹窗
                if not hasattr(self, 'current_aws_request') or not self.current_aws_request:
                    QMessageBox.warning(self, '警告', f'账户 {email} 登录失败，无法获取验证码!')
                self.log_message(f"无法获取验证码: 账户 {email} 登录失败", "WARNING")
                return
            elif status_text == "等待验证":
                # 在自动模式下不显示弹窗
                if not hasattr(self, 'current_aws_request') or not self.current_aws_request:
                    QMessageBox.warning(self, '警告', f'账户 {email} 尚未完成登录验证!')
                self.log_message(f"无法获取验证码: 账户 {email} 尚未完成登录验证", "WARNING")
                return
        except Exception as e:
            self.log_message(f"获取验证码前检查时出错: {str(e)}", "ERROR")
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"获取验证码前检查错误详情: {traceback.format_exc()}\n")
            return
            
        # 立即禁用获取验证码按钮，防止重复点击
        get_code_btn = self.table.cellWidget(row, 4)
        if get_code_btn:
            get_code_btn.setEnabled(False)
            get_code_btn.setText("获取中...")
            write_log(f"已禁用行{row}的获取验证码按钮", "DEBUG")

        # 更新验证码列显示为获取验证码中
        code_item = QTableWidgetItem("正在获取验证码...")
        code_item.setTextAlignment(Qt.AlignCenter)
        code_item.setBackground(QColor(255, 200, 102))  # 橘色
        code_item.setForeground(QColor(139, 69, 0))  # 深橘色文字
        self.table.setItem(row, 3, code_item)
        
        # 将焦点设置到验证码单元格
        self.table.setCurrentCell(row, 3)  # 设置焦点到验证码列
        self.table.setFocus()
        
        # 获取对应账户信息
        try:
            account_found = False
            for account in self.accounts:
                if account[0] == email:
                    account_found = True
                    try:
                        # 创建单独的线程获取验证码
                        thread = QThread()
                        worker = CodeWorker(account[0], account[1], account[2], account[3], self, row)

                        # 存储线程引用，防止被垃圾回收
                        if row in self.code_workers:
                            # 如果已经有线程在运行，强制停止它
                            try:
                                worker_data = self.code_workers[row]
                                old_thread = worker_data.get('thread')
                                old_worker = worker_data.get('worker')

                                if old_thread and hasattr(old_thread, 'isRunning') and old_thread.isRunning():
                                    write_log(f"强制停止旧的验证码获取线程: 行{row}", "INFO")
                                    if old_worker and hasattr(old_worker, 'stop'):
                                        old_worker.stop()

                                    if hasattr(old_thread, 'quit'):
                                        old_thread.quit()
                                    if hasattr(old_thread, 'wait'):
                                        old_thread.wait(2000)  # 等待最多2秒

                                    # 如果线程仍在运行，强制终止
                                    if hasattr(old_thread, 'isRunning') and old_thread.isRunning():
                                        write_log(f"线程未正常退出，强制终止: 行{row}", "WARNING")
                                        if hasattr(old_thread, 'terminate'):
                                            old_thread.terminate()
                                        if hasattr(old_thread, 'wait'):
                                            old_thread.wait(1000)  # 再等待1秒

                                # 清除对象引用
                                self.code_workers.pop(row, None)
                                write_log(f"已清理旧线程引用: 行{row}", "DEBUG")
                            except Exception as e:
                                write_log(f"停止旧线程时出错: {str(e)}", "ERROR")
                                # 即使出错也要清除引用
                                try:
                                    self.code_workers.pop(row, None)
                                except:
                                    pass

                        # 保存新线程
                        self.code_workers[row] = {
                            'thread': thread,
                            'worker': worker
                        }

                        # 连接验证码线程完成信号
                        worker.moveToThread(thread)
                        thread.started.connect(worker.run)

                        # 安全的线程清理逻辑
                        def safe_cleanup():
                            try:
                                if thread.isRunning():
                                    thread.quit()
                                    thread.wait(2000)  # 等待最多2秒
                                # 延迟删除对象，避免立即删除导致的问题
                                QTimer.singleShot(1000, worker.deleteLater)
                                QTimer.singleShot(1000, thread.deleteLater)
                            except Exception as e:
                                write_log(f"线程清理时出错: {str(e)}", "ERROR")

                        worker.finished.connect(safe_cleanup)
                        # 传递row参数以标识哪个账户
                        worker.result_ready.connect(lambda status, code, row=row: self.update_code_result(row, status, code))

                        # 启动线程前，检查是否需要暂停邮件处理线程
                        if hasattr(self, 'email_worker') and self.email_worker and self.email_worker.isRunning() and not self.email_worker.pause_flag:
                            self.log_message(f"暂停账户登录处理线程以获取验证码", "INFO")
                            # 在暂停前记录状态
                            self.current_account_index = self.email_worker.current_index
                            self.log_message(f"保存当前处理索引: {self.current_account_index}", "DEBUG")
                            self.email_worker.pause()
                            self.email_paused = True

                            # 在日志中记录更多细节，方便排查问题
                            self.log_message(f"线程暂停状态: {self.email_worker.pause_flag}", "DEBUG")
                            self.log_message(f"UI暂停标志: {self.email_paused}", "DEBUG")

                        # 暂时禁用线程池，直接使用原有线程系统
                        if False:  # 禁用线程池
                            # 使用新的线程池系统
                            self.start_code_task_with_pool(row, account, email)
                        else:
                            # 使用原有线程系统
                            thread.start()
                            self.log_message(f"成功启动验证码获取线程: {email}", "INFO")
                        break
                    except Exception as e:
                        write_log(f"创建验证码获取线程时出错: {str(e)}", "ERROR")
                        with open(LOG_FILE, "a", encoding="utf-8") as f:
                            f.write(f"创建线程错误详情: {traceback.format_exc()}\n")
                        # 恢复按钮状态和验证码显示
                        get_code_btn = self.table.cellWidget(row, 4)
                        if get_code_btn:
                            get_code_btn.setEnabled(True)
                            get_code_btn.setText("获取验证码")
                            write_log(f"已恢复行{row}的按钮状态（创建线程失败）", "DEBUG")
                        # 清空验证码显示
                        code_item = QTableWidgetItem("创建线程失败")
                        code_item.setTextAlignment(Qt.AlignCenter)
                        code_item.setBackground(QColor(255, 204, 204))  # 淡红色
                        code_item.setForeground(QColor(139, 0, 0))  # 深红色文字
                        self.table.setItem(row, 3, code_item)
                        return

            if not account_found:
                self.log_message(f"未找到账户 {email} 的详细信息", "ERROR")
                # 恢复按钮状态
                get_code_btn = self.table.cellWidget(row, 4)
                if get_code_btn:
                    get_code_btn.setEnabled(True)
                    get_code_btn.setText("获取验证码")

        except Exception as e:
            self.log_message(f"获取验证码时出错: {str(e)}", "ERROR")
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"获取验证码错误详情: {traceback.format_exc()}\n")
            # 恢复按钮状态
            get_code_btn = self.table.cellWidget(row, 4)
            if get_code_btn:
                get_code_btn.setEnabled(True)
                get_code_btn.setText("获取验证码")
    
    def process_finished(self, successful_accounts):
        """处理完成后的回调"""
        # 扩展成功登录账户列表，避免重复
        for account in successful_accounts:
            if account not in self.successful_accounts:
                self.successful_accounts.append(account)
        
        self.log_message(f"处理完成，成功登录 {len(successful_accounts)} 个账户", "SUCCESS")
        
        # 输出登录失败的账户数量
        if hasattr(self, 'failed_accounts') and self.failed_accounts:
            self.log_message(f"处理完成，登录失败 {len(self.failed_accounts)} 个账户", "ERROR")
        
        # 更新文件内容
        self.update_file_content()
        
        # 更新剩余账户数量
        self.update_remaining_count()
        
        # 重新启用加载按钮
        self.load_btn.setEnabled(True)
        self.load_btn.setText("加载账户")
        
        # 显示处理结果
        if successful_accounts:
            QMessageBox.information(self, '处理完成', f'成功登录 {len(successful_accounts)} 个账户')
        else:
            QMessageBox.warning(self, '处理完成', '没有账户登录成功，请检查账户信息')
    
    def update_file_content(self):
        """更新文件内容，将成功登录的账户和失败的账户分别放到底部对应分类下"""
        if not self.current_file or not self.accounts:
            return
            
        try:
            # 确保failed_accounts属性存在
            if not hasattr(self, 'failed_accounts'):
                self.failed_accounts = []
            
            # 读取原文件内容
            with open(self.current_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 收集失败的账户信息
            failed_account_emails = [account[0] for account in self.failed_accounts]

            # 收集成功的账户邮箱地址
            successful_account_emails = [account[0] for account in self.successful_accounts]

            # 调试信息
            self.log_message(f"调试：成功账户邮箱列表 {successful_account_emails}", "DEBUG")
            self.log_message(f"调试：失败账户邮箱列表 {failed_account_emails}", "DEBUG")
            
            # 筛选出未登录的账户、成功登录的账户和失败的账户
            unused_lines = []
            successful_lines = []
            failed_lines = []
            
            # 查找标记位置
            success_marker_index = -1
            failed_marker_index = -1
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                if line_stripped == "已被登录的账户：":
                    success_marker_index = i
                elif line_stripped == "登录失败账户：":
                    failed_marker_index = i
            
            # 处理已有的内容
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # 跳过空行和标记行
                if not line or line == "已被登录的账户：" or line == "登录失败账户：":
                    i += 1
                    continue
                    
                # 如果当前行是在"已被登录的账户："标记之后，且在"登录失败账户："标记之前
                if success_marker_index != -1 and i > success_marker_index and (failed_marker_index == -1 or i < failed_marker_index):
                    # 检查是否是失败账户，如果是则移到失败列表
                    parts = line.split('----')
                    if len(parts) >= 4 and parts[0] in failed_account_emails:  # 修改为支持4个或更多字段
                        failed_lines.append(line)
                    else:
                        successful_lines.append(line)
                    i += 1
                    continue
                    
                # 如果当前行是在"登录失败账户："标记之后
                if failed_marker_index != -1 and i > failed_marker_index:
                    # 检查是否是账户行，并且是否已经重新登录成功
                    parts = line.split('----')
                    if len(parts) >= 4:
                        # 检查是否已经重新登录成功
                        is_successful = False
                        for successful_account in self.successful_accounts:
                            if successful_account[0] == parts[0]:  # 通过邮箱地址匹配
                                is_successful = True
                                break

                        if is_successful:
                            successful_lines.append(line)
                            self.log_message(f"将重新登录成功的账户从失败列表移到成功列表: {parts[0]}", "INFO")
                        else:
                            failed_lines.append(line)
                    else:
                        failed_lines.append(line)
                    i += 1
                    continue
                    
                # 其他情况，判断是否是账户行
                parts = line.split('----')
                if len(parts) >= 4:  # 修改为支持4个或更多字段
                    # 只取前4个字段进行比较
                    account = parts[:4]
                    if account[0] in failed_account_emails:
                        failed_lines.append(line)
                    else:
                        # 检查是否在成功账户列表中（通过邮箱地址匹配）
                        is_successful = False
                        for successful_account in self.successful_accounts:
                            if successful_account[0] == account[0]:  # 通过邮箱地址匹配
                                is_successful = True
                                break

                        if is_successful:
                            successful_lines.append(line)
                        else:
                            unused_lines.append(line)
                else:
                    unused_lines.append(line)
                    
                i += 1
            
            # 写入更新后的内容
            with open(self.current_file, 'w', encoding='utf-8') as f:
                # 写入未登录的账户
                for line in unused_lines:
                    f.write(line + '\n')
                
                # 写入登录成功的账户标记和内容
                f.write('\n已被登录的账户：\n')
                for line in successful_lines:
                    f.write(line + '\n')
                    
                # 写入登录失败的账户标记和内容
                f.write('\n登录失败账户：\n')
                for line in failed_lines:
                    f.write(line + '\n')
            
            # 输出统计信息和调试信息
            self.log_message(f"文件更新完成：成功账户 {len(successful_lines)}，失败账户 {len(failed_lines)}，未处理账户 {len(unused_lines)}")
            self.log_message(f"调试信息：成功账户列表长度 {len(self.successful_accounts)}", "DEBUG")
            if self.successful_accounts:
                self.log_message(f"调试信息：第一个成功账户 {self.successful_accounts[0]}", "DEBUG")
        
            self.log_message("已更新文件内容")
        except Exception as e:
            self.log_message(f"更新文件内容出错: {str(e)}", "ERROR")
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"错误详情: {traceback.format_exc()}\n")
    
    def update_remaining_count(self):
        """更新剩余账户数量，只计算符合格式且未被分类的账户"""
        if not self.current_file:
            return
            
        try:
            # 读取文件内容
            with open(self.current_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 统计符合格式且未被分类的账户
            remaining = 0
            in_success_section = False
            in_failed_section = False
            
            for line in lines:
                line_stripped = line.strip()
                
                # 检查是否进入特殊分类区域
                if line_stripped == "已被登录的账户：":
                    in_success_section = True
                    continue
                elif line_stripped == "登录失败账户：":
                    in_failed_section = True
                    continue
                    
                # 跳过空行
                if not line_stripped:
                    continue
                    
                # 如果不在特殊分类区域，检查是否符合格式
                if not in_success_section and not in_failed_section:
                    parts = line_stripped.split('----')
                    if len(parts) >= 4:  # 修改为支持4个或更多字段
                        remaining += 1
            
            self.remaining_label.setText(f"剩余: {remaining}")
            
            # 记录日志
            self.log_message(f"更新剩余账户数量: {remaining}")
        except Exception as e:
            self.log_message(f"更新剩余账户数量出错: {str(e)}", "ERROR")
            with open(LOG_FILE, "a", encoding="utf-8") as f:
                f.write(f"错误详情: {traceback.format_exc()}\n")
    
    def log_message(self, message, level="INFO"):
        """记录操作日志到UI和文件"""
        try:
            current_time = time.strftime("%H:%M:%S", time.localtime())
            log_entry = f"[{current_time}] {message}"
            
            # 在文本区域添加日志
            if hasattr(self, 'log_text') and self.log_text is not None:
                # 根据日志级别设置不同颜色
                color = "black"
                if level == "ERROR":
                    color = "red"
                elif level == "WARNING":
                    color = "orange"
                elif level == "SUCCESS":
                    color = "green"
                
                self.log_text.append(f'<span style="color:{color};">{log_entry}</span>')
                
                # 滚动到底部
                scrollbar = self.log_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())
            
            # 写入到日志文件
            write_log(message, level)
        except Exception as e:
            print(f"记录日志时出错: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止文件监控
        if hasattr(self, 'observer') and self.observer.is_alive():
            self.observer.stop()
            self.observer.join()
            write_log("AWS文件监控已停止", "INFO")

        # 清理线程池（暂时禁用）
        # if hasattr(self, 'thread_pool_manager'):
        #     write_log("正在清理线程池...", "INFO")
        #     self.thread_pool_manager.clear()
        #     if self.thread_pool_manager.wait_for_done(5000):  # 等待最多5秒
        #         write_log("线程池已清理完成", "INFO")
        #     else:
        #         write_log("线程池清理超时", "WARNING")

        # 停止主验证线程
        if hasattr(self, 'email_worker') and self.email_worker.isRunning():
            self.email_worker.stop()
            self.email_worker.wait()

        # 停止所有验证码获取线程
        try:
            if hasattr(self, 'code_workers'):
                for row_id, worker_data in self.code_workers.items():
                    try:
                        thread = worker_data['thread']
                        worker = worker_data['worker']
                        if thread.isRunning():
                            worker.stop()
                            thread.quit()
                            thread.wait(1000)  # 等待最多1秒
                    except Exception as e:
                        print(f"关闭线程时出错: {str(e)}")
                        
            # 停止所有重新登录线程
            if hasattr(self, 'retry_workers'):
                for row_id, worker_data in self.retry_workers.items():
                    try:
                        thread = worker_data['thread']
                        worker = worker_data['worker']
                        if thread.isRunning():
                            worker.stop()
                            thread.quit()
                            thread.wait(1000)  # 等待最多1秒
                    except Exception as e:
                        print(f"关闭重新登录线程时出错: {str(e)}")
                        
            self.log_message("应用程序关闭")
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            
        event.accept()

    def start_code_task_with_pool(self, row, account, email):
        """使用线程池启动验证码获取任务"""
        try:
            # 检查是否已有任务在运行
            if row in self.active_code_tasks:
                old_task_id = self.active_code_tasks[row]
                write_log(f"停止旧的验证码获取任务: 行{row}, 任务ID: {old_task_id}", "INFO")

            # 创建验证码获取任务
            def code_callback(status, code, task_id):
                # 在主线程中处理结果
                QTimer.singleShot(0, lambda: self.update_code_result(row, status, code))
                # 清理任务引用
                if row in self.active_code_tasks and self.active_code_tasks[row] == task_id:
                    del self.active_code_tasks[row]
                self.thread_pool_manager.remove_task(task_id)

            task = CodeTask(account[0], account[1], account[2], account[3], code_callback)
            task_id = self.thread_pool_manager.submit_task(task, f"code_task_{row}")
            self.active_code_tasks[row] = task_id

            # 检查是否需要暂停邮件处理线程
            if hasattr(self, 'email_worker') and self.email_worker and self.email_worker.isRunning() and not self.email_worker.pause_flag:
                self.log_message(f"暂停账户登录处理线程以获取验证码", "INFO")
                self.current_account_index = self.email_worker.current_index
                self.log_message(f"保存当前处理索引: {self.current_account_index}", "DEBUG")
                self.email_worker.pause()
                self.email_paused = True

            self.log_message(f"成功提交验证码获取任务: {email}, 任务ID: {task_id}", "INFO")

        except Exception as e:
            write_log(f"创建验证码获取任务时出错: {str(e)}", "ERROR")
            # 回退到原有方法
            self.log_message(f"回退到原有线程方法", "WARNING")

    def start_login_task_with_pool(self, row, account, email):
        """使用线程池启动重新登录任务"""
        try:
            # 检查是否已有任务在运行
            if row in self.active_login_tasks:
                old_task_id = self.active_login_tasks[row]
                write_log(f"停止旧的重新登录任务: 行{row}, 任务ID: {old_task_id}", "INFO")

            # 创建重新登录任务
            def login_callback(success, message, task_id):
                # 在主线程中处理结果
                QTimer.singleShot(0, lambda: self.handle_retry_result(row, success, message))
                # 清理任务引用
                if row in self.active_login_tasks and self.active_login_tasks[row] == task_id:
                    del self.active_login_tasks[row]
                self.thread_pool_manager.remove_task(task_id)

            task = LoginTask(account, login_callback)
            task_id = self.thread_pool_manager.submit_task(task, f"login_task_{row}")
            self.active_login_tasks[row] = task_id

            self.log_message(f"成功提交重新登录任务: {email}, 任务ID: {task_id}", "INFO")

        except Exception as e:
            write_log(f"创建重新登录任务时出错: {str(e)}", "ERROR")
            # 回退到原有方法
            self.log_message(f"回退到原有线程方法", "WARNING")

    def update_code_result(self, row, status, code):
        """更新验证码获取结果，改进样式显示"""
        try:
            write_log(f"更新验证码结果: 行{row}, 状态{status}, 验证码{code}", "DEBUG")

            # 验证验证码格式，防止错误信息被当作验证码
            is_valid_code = False
            if code and isinstance(code, str):
                # 检查是否为6位数字验证码
                if len(code) == 6 and code.isdigit() and code != "000000":
                    is_valid_code = True
                # 检查是否为特殊状态（如"已被注册"）
                elif code in ["已被注册"]:
                    is_valid_code = True
                else:
                    # 如果code包含错误信息，记录警告
                    if any(keyword in code.lower() for keyword in ["错误", "error", "exception", "timeout", "failed"]):
                        write_log(f"警告：检测到错误信息被当作验证码: {code}", "WARNING")
                        # 修正状态和验证码
                        status = f"获取失败: {code}"
                        code = ""
                        is_valid_code = False

            # 更新验证码列显示验证码
            if code and is_valid_code:
                code_item = QTableWidgetItem(code)
                code_item.setTextAlignment(Qt.AlignCenter)
                # 成功获取验证码时使用淡蓝色背景
                code_item.setBackground(QColor(173, 216, 230))  # 淡蓝色
                code_item.setForeground(QColor(0, 0, 139))  # 深蓝色文字

                # 设置字体为粗体，使验证码更明显
                font = code_item.font()
                font.setBold(True)
                font.setPointSize(10)  # 稍微大一点的字体
                code_item.setFont(font)

                self.table.setItem(row, 3, code_item)

                # 如果获取到验证码，自动复制到剪贴板
                self.clipboard.setText(code)
                self.log_message(f"验证码 {code} 已复制到剪贴板")
            else:
                # 清空验证码单元格，添加淡红色背景表示失败
                display_text = "未找到验证码" if not code else f"获取失败"
                code_item = QTableWidgetItem(display_text)
                code_item.setTextAlignment(Qt.AlignCenter)
                code_item.setBackground(QColor(255, 204, 204))  # 淡红色
                code_item.setForeground(QColor(139, 0, 0))  # 深红色文字
                self.table.setItem(row, 3, code_item)

            # 恢复获取验证码按钮状态
            get_code_btn = self.table.cellWidget(row, 4)
            if get_code_btn:
                get_code_btn.setEnabled(True)
                get_code_btn.setText("获取验证码")
                write_log(f"已恢复行{row}的按钮状态", "DEBUG")

            # 记录日志
            result_msg = f"行 {row+1}: {status}" + (f", 验证码: {code}" if code and is_valid_code else "")
            self.log_message(result_msg)

            # 检查是否是AWS请求的响应（支持多线程并发）
            write_log(f"检查AWS请求响应: aws_requests={list(self.aws_requests.keys()) if hasattr(self, 'aws_requests') else 'None'}, row={row}", "DEBUG")
            write_log(f"验证码结果详情: status='{status}', code='{code}', is_valid_code={is_valid_code}", "DEBUG")

            if hasattr(self, 'aws_requests') and row in self.aws_requests:
                # 获取该行的请求信息
                current_request = self.aws_requests[row].copy()

                write_log(f"找到行{row}的AWS请求: {current_request}", "DEBUG")
                write_log(f"处理AWS响应: status={status}, code={code}, is_valid_code={is_valid_code}", "DEBUG")

                # 检查是否已经在CodeWorker中处理过了
                if code and is_valid_code and status == "验证码获取成功":
                    write_log(f"验证码获取成功，但可能已在CodeWorker中处理，检查是否需要补充处理", "INFO")
                    # 如果AWS请求仍然存在，说明CodeWorker中的立即处理可能失败了，这里作为备用
                    if row in self.aws_requests:
                        thread_id = current_request.get('thread_id', 0)
                        is_multithread = current_request.get('is_multithread', False)
                        timestamp = current_request.get('timestamp', '')

                        write_log(f"备用处理：写入成功响应: thread_id={thread_id}, is_multithread={is_multithread}", "DEBUG")
                        self.write_response("Success", code, timestamp, thread_id, is_multithread)
                        write_log(f"备用处理：已响应AWS请求，验证码: {code}", "SUCCESS")
                        # 清除该行的请求
                        write_log(f"备用处理：清除行{row}的AWS请求状态", "DEBUG")
                        self.aws_requests.pop(row, None)
                    else:
                        write_log(f"AWS请求已在CodeWorker中处理完成", "INFO")
                elif status == "获取到无效验证码":
                    # 无效验证码，不响应AWS请求，继续等待
                    write_log(f"获取到无效验证码 {code}，继续等待有效验证码", "WARNING")
                    # 不清除AWS请求，继续等待
                    # 不恢复按钮状态，保持禁用状态
                    return  # 直接返回，不执行后续的按钮恢复逻辑
                elif status == "已停止":
                    # 线程被停止，检查是否需要备用处理
                    if row in self.aws_requests:
                        thread_id = current_request.get('thread_id', 0)
                        is_multithread = current_request.get('is_multithread', False)
                        timestamp = current_request.get('timestamp', '')

                        self.write_response("Error", "获取验证码被停止", timestamp, thread_id, is_multithread)
                        write_log(f"备用处理：AWS请求失败: 获取验证码被停止", "ERROR")
                        # 清除该行的请求
                        write_log(f"备用处理：清除行{row}的AWS请求状态", "DEBUG")
                        self.aws_requests.pop(row, None)
                    else:
                        write_log(f"线程停止，但AWS请求已在CodeWorker中处理", "INFO")
                else:
                    # 处理其他错误情况，检查是否需要备用处理
                    if row in self.aws_requests:
                        thread_id = current_request.get('thread_id', 0)
                        is_multithread = current_request.get('is_multithread', False)
                        timestamp = current_request.get('timestamp', '')

                        # 如果status包含错误信息，将其作为错误消息传递
                        error_message = "未找到验证码"
                        if "错误:" in status:
                            error_message = status.replace("错误:", "").strip()
                        elif status not in ["验证码获取成功", "获取到无效验证码", "已停止"]:
                            error_message = status

                        self.write_response("Error", error_message, timestamp, thread_id, is_multithread)
                        write_log(f"备用处理：AWS请求失败: {error_message}", "ERROR")
                        # 清除该行的请求
                        write_log(f"备用处理：清除行{row}的AWS请求状态", "DEBUG")
                        self.aws_requests.pop(row, None)
                    else:
                        write_log(f"错误状态，但AWS请求已在CodeWorker中处理: {status}", "INFO")
            else:
                write_log(f"行{row}无对应的AWS请求", "DEBUG")

                # 检查是否是无效验证码的情况
                if status == "获取到无效验证码":
                    # 无效验证码，检查是否有其他行的AWS请求等待该邮箱
                    target_email = None
                    if row < self.table.rowCount():
                        email_item = self.table.item(row, 0)
                        if email_item:
                            target_email = email_item.text()

                    # 检查是否有其他行的AWS请求在等待这个邮箱
                    waiting_requests = [r for r, req in self.aws_requests.items() if req.get('email') == target_email]
                    if waiting_requests:
                        write_log(f"获取到无效验证码 {code}，但有AWS请求等待，继续等待有效验证码", "WARNING")
                        # 不恢复按钮状态，保持禁用状态
                        return  # 直接返回，不执行后续的按钮恢复逻辑
                    else:
                        write_log(f"获取到无效验证码 {code}，无AWS请求等待", "INFO")

                # 如果没有AWS请求但获取到了有效验证码，检查是否需要写入响应文件（兼容手动操作）
                elif code and is_valid_code and status == "验证码获取成功":
                    write_log(f"验证码获取成功但无AWS请求，检查响应文件需求", "INFO")

                    # 检查单线程响应文件
                    response_file = RESPONSE_FILE
                    if os.path.exists(response_file):
                        try:
                            # 读取现有内容检查格式
                            with open(response_file, 'r', encoding='utf-8') as f:
                                existing_content = f.read().strip()

                            write_log(f"发现响应文件，当前内容: {existing_content}", "INFO")

                            # 如果文件存在但内容不是成功的验证码，更新它
                            if existing_content and not existing_content.startswith("success|"):
                                timestamp = str(int(time.time() * 1000))
                                self.write_response("success", code, timestamp, 0, False)
                                write_log(f"手动获取验证码成功，已更新当前目录响应文件: {code}", "INFO")
                        except Exception as e:
                            write_log(f"检查响应文件时出错: {str(e)}", "ERROR")
                    else:
                        # 如果响应文件不存在，创建新的响应文件
                        timestamp = str(int(time.time() * 1000))
                        self.write_response("Success", code, timestamp, 0, False)
                        write_log(f"验证码获取成功，创建新的响应文件: {code}", "INFO")
                    
                    # 验证码获取成功，不再需要继续处理
                    write_log(f"验证码获取成功，停止后续处理", "INFO")
                elif status == "已停止":
                    # 线程被停止，但没有对应的AWS请求，只记录日志
                    write_log(f"行{row}验证码获取被停止，但无对应AWS请求", "INFO")
                else:
                    # 其他状态，只记录日志
                    write_log(f"行{row}验证码获取状态: {status}，无对应AWS请求", "INFO")

            # 清理线程引用，防止内存泄漏
            if row in self.code_workers:
                try:
                    worker_data = self.code_workers[row]
                    thread = worker_data.get('thread')
                    worker = worker_data.get('worker')

                    # 安全检查线程状态，避免访问已删除的QThread对象
                    thread_running = False
                    try:
                        if thread and hasattr(thread, 'isRunning'):
                            thread_running = thread.isRunning()
                    except Exception as e:
                        # 如果访问线程状态出错，认为线程已经结束
                        write_log(f"更新结果时检查线程状态出错，认为线程已结束: {str(e)}", "DEBUG")
                        thread_running = False

                    if not thread_running:
                        write_log(f"清理已完成的线程引用: 行{row}", "DEBUG")
                        self.code_workers.pop(row, None)
                    elif thread_running:
                        write_log(f"线程仍在运行，稍后清理: 行{row}", "DEBUG")
                        # 使用定时器延迟清理，但缩短时间
                        QTimer.singleShot(2000, lambda: self.cleanup_thread_reference(row))
                except Exception as e:
                    write_log(f"清理线程引用时出错: {str(e)}", "ERROR")
            
            # 如果验证码获取成功，立即清理AWS请求状态
            if status == "验证码获取成功" and code and is_valid_code:
                if row in self.aws_requests:
                    write_log(f"验证码获取成功，立即清理行{row}的AWS请求状态", "DEBUG")
                    self.aws_requests.pop(row, None)
            
            # 将焦点设置到验证码单元格
            self.table.setCurrentCell(row, 3)  # 设置焦点到验证码列
            self.table.setFocus()
            
            # 恢复登录处理线程
            if hasattr(self, 'email_worker') and self.email_worker.isRunning():
                if self.email_worker.pause_flag:
                    self.log_message("准备恢复账户登录处理线程...", "INFO")
                    # 延迟恢复，确保UI更新完成
                    QTimer.singleShot(1000, self.resume_email_worker)
        
        except Exception as e:
            self.log_message(f"更新验证码结果时出错: {str(e)}")
            # 出错时清空验证码单元格
            code_item = QTableWidgetItem("发生错误")
            code_item.setTextAlignment(Qt.AlignCenter)
            code_item.setBackground(QColor(255, 204, 204))  # 淡红色
            code_item.setForeground(QColor(139, 0, 0))  # 深红色文字
            self.table.setItem(row, 3, code_item)
            
            # 恢复获取验证码按钮状态
            get_code_btn = self.table.cellWidget(row, 4)
            if get_code_btn:
                get_code_btn.setEnabled(True)
                get_code_btn.setText("获取验证码")
            
            # 恢复登录处理线程（即使出错也恢复）
            if hasattr(self, 'email_worker') and self.email_worker.isRunning():
                if self.email_worker.pause_flag:
                    self.log_message("尝试恢复账户登录处理（错误恢复）", "INFO")
                    QTimer.singleShot(500, self.resume_email_worker)  # 使用定时器延迟恢复，确保UI更新

    def retry_login(self, row):
        """重新尝试登录失败的账户"""
        try:
            # 获取当前行的邮箱地址
            email_item = self.table.item(row, 0)
            if not email_item:
                self.log_message(f"行 {row} 没有邮箱信息", "ERROR")
                return
            
            email = email_item.text()
            self.log_message(f"尝试重新登录账户: {email}", "INFO")
            
            # 查找对应的账户信息
            target_account = None
            for account in self.accounts:
                if account[0] == email:
                    target_account = account
                    break
            
            if not target_account:
                self.log_message(f"未找到账户 {email} 的详细信息", "ERROR")
                return
            
            # 先禁用重新登录按钮，防止重复点击
            # 获取按钮引用
            retry_btn = self.table.cellWidget(row, 2)
            if retry_btn:
                retry_btn.setEnabled(False)
                retry_btn.setText("登录中...")
            
            # 从失败账户列表中移除
            if target_account in self.failed_accounts:
                self.failed_accounts.remove(target_account)
                self.log_message(f"已从失败账户列表中移除: {email}", "INFO")
            
            # 更新状态为"登录中"
            status_item = QTableWidgetItem("登录中")
            status_item.setTextAlignment(Qt.AlignCenter)
            # 设置为不可编辑
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            status_item.setBackground(QColor(255, 200, 102))  # 橘色
            status_item.setForeground(QColor(139, 69, 0))  # 深橘色文字
            self.table.setItem(row, 2, status_item)
            
            try:
                # 暂时禁用线程池，直接使用原有线程系统
                if False:  # 禁用线程池
                    # 使用新的线程池系统
                    self.start_login_task_with_pool(row, target_account, email)
                else:
                    # 使用原有线程系统
                    # 检查是否已有线程在运行，先清理旧线程
                    if row in self.retry_workers:
                        try:
                            worker_data = self.retry_workers[row]
                            old_thread = worker_data.get('thread')
                            old_worker = worker_data.get('worker')

                            if old_thread and hasattr(old_thread, 'isRunning') and old_thread.isRunning():
                                write_log(f"强制停止旧的登录线程: 行{row}", "INFO")
                                if old_worker and hasattr(old_worker, 'stop'):
                                    old_worker.stop()

                                if hasattr(old_thread, 'quit'):
                                    old_thread.quit()
                                if hasattr(old_thread, 'wait'):
                                    old_thread.wait(2000)  # 等待最多2秒

                                # 如果线程仍在运行，强制终止
                                if hasattr(old_thread, 'isRunning') and old_thread.isRunning():
                                    write_log(f"线程未正常退出，强制终止: 行{row}", "WARNING")
                                    if hasattr(old_thread, 'terminate'):
                                        old_thread.terminate()
                                    if hasattr(old_thread, 'wait'):
                                        old_thread.wait(1000)  # 再等待1秒

                            # 清除对象引用
                            self.retry_workers.pop(row, None)
                            write_log(f"已清理旧重新登录线程引用: 行{row}", "DEBUG")
                        except Exception as e:
                            write_log(f"停止旧重新登录线程时出错: {str(e)}", "ERROR")
                            # 即使出错也要清除引用
                            try:
                                self.retry_workers.pop(row, None)
                            except:
                                pass

                    # 创建单独的线程进行登录
                    thread = QThread()
                    worker = SingleLoginWorker(target_account)
                    worker.moveToThread(thread)
                    thread.started.connect(worker.run)

                    # 保存线程引用
                    self.retry_workers[row] = {
                        'thread': thread,
                        'worker': worker
                    }

                    # 安全的线程清理逻辑
                    def safe_cleanup():
                        try:
                            if thread.isRunning():
                                thread.quit()
                                thread.wait(2000)  # 等待最多2秒
                            # 延迟删除对象，避免立即删除导致的问题
                            QTimer.singleShot(1000, worker.deleteLater)
                            QTimer.singleShot(1000, thread.deleteLater)
                            # 清理线程引用
                            QTimer.singleShot(1000, lambda r=row: self.cleanup_retry_thread(r))
                        except Exception as e:
                            write_log(f"线程清理时出错: {str(e)}", "ERROR")

                    worker.finished.connect(safe_cleanup)
                    worker.result_ready.connect(lambda success, message, r=row: self.handle_retry_result(r, success, message))

                    # 启动线程
                    thread.start()
                    self.log_message(f"成功启动重新登录线程: {email}", "INFO")
                
            except Exception as e:
                write_log(f"创建重新登录线程时出错: {str(e)}", "ERROR")
                with open(LOG_FILE, "a", encoding="utf-8") as f:
                    f.write(f"创建线程错误详情: {traceback.format_exc()}\n")
                # 恢复为重试按钮
                retry_btn = QPushButton("重新登录")
                retry_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #ffcccc;
                        color: #8b0000;
                        border: 1px solid #ff9999;
                        border-radius: 4px;
                        padding: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #ff9999;
                    }
                    QPushButton:pressed {
                        background-color: #ff6666;
                    }
                """)
                retry_btn.clicked.connect(lambda _, r=row: self.retry_login(r))
                self.table.setCellWidget(row, 2, retry_btn)
            
        except Exception as e:
            self.log_message(f"重新登录时出错: {str(e)}", "ERROR")
            # 恢复为重试按钮
            retry_btn = QPushButton("重新登录")
            retry_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffcccc;
                    color: #8b0000;
                    border: 1px solid #ff9999;
                    border-radius: 4px;
                    padding: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #ff9999;
                }
                QPushButton:pressed {
                    background-color: #ff6666;
                }
            """)
            retry_btn.clicked.connect(lambda _, r=row: self.retry_login(r))
            self.table.setCellWidget(row, 2, retry_btn)
            
    def cleanup_retry_thread(self, row):
        """清理重新登录线程引用"""
        try:
            if row in self.retry_workers:
                worker_data = self.retry_workers[row]
                thread = worker_data.get('thread')
                
                # 安全检查线程状态
                thread_running = False
                try:
                    if thread and hasattr(thread, 'isRunning'):
                        thread_running = thread.isRunning()
                except:
                    # 如果检查线程状态出错，认为线程已经结束
                    thread_running = False
                    
                if not thread_running:
                    write_log(f"延迟清理重新登录线程引用: 行{row}", "DEBUG")
                    try:
                        self.retry_workers.pop(row, None)
                    except:
                        pass
                else:
                    write_log(f"重新登录线程仍在运行，无法清理: 行{row}", "WARNING")
        except Exception as e:
            write_log(f"延迟清理重新登录线程引用时出错: {str(e)}", "ERROR")
            # 即使出错也尝试清理
            try:
                if row in self.retry_workers:
                    self.retry_workers.pop(row, None)
            except:
                pass

    def handle_retry_result(self, row, success, message):
        """处理重新登录的结果"""
        try:
            # 获取当前行的邮箱地址
            email_item = self.table.item(row, 0)
            if not email_item:
                self.log_message(f"行 {row} 没有邮箱信息", "ERROR")
                return
            
            email = email_item.text()
            
            # 查找对应的账户信息
            target_account = None
            for account in self.accounts:
                if account[0] == email:
                    target_account = account
                    break
            
            if not target_account:
                self.log_message(f"未找到账户 {email} 的详细信息", "ERROR")
                return
            
            if success:
                # 登录成功
                self.log_message(f"账户 {email} 重新登录成功: {message}", "SUCCESS")
                
                # 先移除当前的按钮控件
                if self.table.cellWidget(row, 2):
                    # 移除当前的按钮控件
                    self.table.removeCellWidget(row, 2)
                
                # 更新状态为"登录成功"
                status_item = QTableWidgetItem("登录成功")
                status_item.setTextAlignment(Qt.AlignCenter)
                # 设置为不可编辑
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                status_item.setBackground(QColor(173, 216, 230))  # 淡蓝色
                status_item.setForeground(QColor(0, 0, 139))  # 深蓝色文字
                self.table.setItem(row, 2, status_item)
                
                # 将账户添加到成功列表
                if target_account not in self.successful_accounts:
                    self.successful_accounts.append(target_account)
                
                # 启用获取验证码按钮
                get_code_btn = self.table.cellWidget(row, 4)
                if get_code_btn:
                    get_code_btn.setEnabled(True)
                
                # 更新文件内容，将账户从失败列表移到成功列表
                self.update_file_content()
            else:
                # 登录失败
                self.log_message(f"账户 {email} 重新登录失败: {message}", "ERROR")
                
                # 恢复为重试按钮
                retry_btn = QPushButton("重新登录")
                retry_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #ffcccc;
                        color: #8b0000;
                        border: 1px solid #ff9999;
                        border-radius: 4px;
                        padding: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #ff9999;
                    }
                    QPushButton:pressed {
                        background-color: #ff6666;
                    }
                """)
                retry_btn.clicked.connect(lambda _, r=row: self.retry_login(r))
                self.table.setCellWidget(row, 2, retry_btn)
                
                # 将账户添加回失败列表
                if target_account not in self.failed_accounts:
                    self.failed_accounts.append(target_account)
        except Exception as e:
            self.log_message(f"处理重新登录结果时出错: {str(e)}", "ERROR")

    def resume_email_worker(self):
        """恢复邮件处理线程的辅助方法"""
        if hasattr(self, 'email_worker') and self.email_worker.isRunning():
            try:
                # 确保线程对象有效
                if self.email_worker.pause_flag:
                    self.log_message("正在恢复邮件处理线程...", "INFO")
                    # 使用事件循环确保UI更新完成后再恢复线程
                    self.email_worker.resume()
                    self.email_paused = False
                    # 添加一条延迟日志，确认线程已真正恢复
                    QTimer.singleShot(1000, lambda: self.log_message("邮件处理线程已成功恢复运行", "SUCCESS"))
            except Exception as e:
                self.log_message(f"恢复线程时出错: {str(e)}", "ERROR")
                with open(LOG_FILE, "a", encoding="utf-8") as f:
                    f.write(f"恢复线程错误详情: {traceback.format_exc()}\n")
    
    def show_initial_help(self):
        """显示初始使用提示"""
        try:
            if not hasattr(self, 'log_text') or self.log_text is None:
                print("警告: log_text 不存在，无法显示初始帮助")
                return
                
            welcome_text = """
<h3>AWS验证码获取工具使用指南</h3>
<p>请按照以下步骤操作：</p>
<ol>
  <li>点击"选择文件"按钮，选择包含微软邮箱账户的文本文件</li>
  <li>文件格式应为：每行一个账户，格式为"邮箱账号----密码----刷新令牌----Client ID"</li>
  <li>输入要提取的账户数量（留空则全部提取）</li>
  <li>点击"加载账户"按钮开始处理</li>
  <li>处理完成后，点击"获取验证码"按钮获取AWS验证码</li>
</ol>
<p>注意：本工具使用OAuth2授权方式登录微软邮箱，请确保提供正确的刷新令牌和客户端ID。</p>
"""
            self.log_text.clear()
            self.log_text.setHtml(welcome_text)
            
            # 使用 QTimer 延迟显示初始消息，确保UI已完全加载
            QTimer.singleShot(100, lambda: self.log_message("软件已初始化，请按照上方指南操作"))
        except Exception as e:
            print(f"显示初始帮助时出错: {str(e)}")

    def table_key_press(self, event):
        """处理表格的键盘事件"""
        # 处理Ctrl+C复制功能
        if event.key() == Qt.Key_C and event.modifiers() == Qt.ControlModifier:
            self.copy_selected_cells()
        else:
            # 如果不是Ctrl+C，则调用原始的keyPressEvent方法
            QTableWidget.keyPressEvent(self.table, event)
            
    def show_context_menu(self, position):
        """显示上下文菜单"""
        menu = QMenu(self)
        
        # 获取选中的单元格
        selected_items = self.table.selectedItems()
            
        if selected_items:
            copy_action = QAction("复制选中内容", self)
            copy_action.triggered.connect(self.copy_selected_cells)
            menu.addAction(copy_action)
            
            # 检查是否选中了验证码所在列的单元格
            for item in selected_items:
                if item.column() == 3:  # 验证码列
                    copy_code_action = QAction("仅复制验证码", self)
                    copy_code_action.triggered.connect(self.copy_verification_code)
                    menu.addAction(copy_code_action)
                    break
                    
            menu.exec_(self.table.viewport().mapToGlobal(position))
    
    def copy_selected_cells(self):
        """复制选中单元格的数据，以智能方式处理格式"""
        selected_items = self.table.selectedItems()
        
        # 没有选中任何单元格，直接返回
        if not selected_items:
            return
            
        # 分析选中的单元格，按行分组
        rows_data = {}
        for item in selected_items:
            row = item.row()
            col = item.column()
            if row not in rows_data:
                rows_data[row] = []
            
            # 保存单元格内容和列索引，以便后续按列排序
            rows_data[row].append((col, item.text()))
        
        # 构建复制文本
        copied_text = ""
        
        # 如果只选择了一行中的多个单元格，将它们以空格分隔
        if len(rows_data) == 1:
            row_data = list(rows_data.values())[0]
            # 按列排序
            row_data.sort(key=lambda x: x[0])
            # 提取文本，用空格连接
            cell_texts = [data[1] for data in row_data]
            copied_text = " ".join(cell_texts)
        # 如果选择了多行，每行一个单独一行，行内元素用空格分隔
        else:
            for row in sorted(rows_data.keys()):
                row_data = rows_data[row]
                # 按列排序
                row_data.sort(key=lambda x: x[0])
                # 提取文本，用空格连接
                cell_texts = [data[1] for data in row_data]
                row_text = " ".join(cell_texts)
                copied_text += row_text + "\n"
            # 移除最后的换行符
            copied_text = copied_text.rstrip("\n")
        
        # 将文本复制到剪贴板
        if copied_text:
            self.clipboard.setText(copied_text)
            self.log_message(f"已复制 {len(selected_items)} 个单元格内容到剪贴板")
            
    def copy_verification_code(self):
        """只复制选中行的验证码"""
        codes = []
        for item in self.table.selectedItems():
            if item.column() == 3:  # 验证码列
                if item and item.text() and item.text() != "正在获取验证码..." and item.text() != "未找到验证码" and item.text() != "发生错误":
                    codes.append(item.text())
        
        if codes:
            clipboard_text = " ".join(codes)
            self.clipboard.setText(clipboard_text)
            self.log_message(f"已复制 {len(codes)} 个验证码到剪贴板: {clipboard_text}")


# 改进的任务基类
class BaseTask(QRunnable):
    """基础任务类，用于线程池"""

    def __init__(self, task_id=None):
        super().__init__()
        self.task_id = task_id
        self.stop_flag = False
        self.setAutoDelete(True)  # 任务完成后自动删除

    def stop(self):
        """停止任务"""
        self.stop_flag = True

# 验证码获取任务
class CodeTask(BaseTask):
    """验证码获取任务"""

    def __init__(self, email, password, refresh_token, client_id, callback, task_id=None):
        super().__init__(task_id)
        self.email = email
        self.password = password
        self.refresh_token = refresh_token
        self.client_id = client_id
        self.callback = callback

    def run(self):
        """执行验证码获取"""
        try:
            write_log(f"开始获取验证码: {self.email}", "INFO")

            if self.stop_flag:
                self.callback("已停止", "", self.task_id)
                return

            # 获取访问令牌
            access_token = self.get_access_token(self.client_id, self.refresh_token)
            if not access_token:
                self.callback("获取令牌失败", "", self.task_id)
                return

            if self.stop_flag:
                self.callback("已停止", "", self.task_id)
                return

            # 获取验证码
            code = self.get_aws_verification_code(self.email, access_token)
            if code:
                if code == "已被注册":
                    self.callback("邮箱已被注册", code, self.task_id)
                else:
                    self.callback("验证码获取成功", code, self.task_id)
            else:
                self.callback("未找到验证码", "", self.task_id)

        except Exception as e:
            write_log(f"验证码获取任务出错: {str(e)}", "ERROR")
            self.callback("获取验证码出错", str(e), self.task_id)

    def get_access_token(self, client_id, refresh_token):
        """获取访问令牌"""
        try:
            data = {
                'client_id': client_id,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            response = requests.post('https://login.live.com/oauth20_token.srf', data=data, timeout=10)

            if response.status_code == 200:
                return response.json()['access_token']
            return None
        except Exception as e:
            write_log(f"获取访问令牌失败: {str(e)}", "ERROR")
            return None

    def generate_auth_string(self, user, token):
        """生成OAuth2认证字符串"""
        auth_string = f"user={user}\1auth=Bearer {token}\1\1"
        return auth_string

    def get_aws_verification_code(self, email_address, access_token):
        aws_senders = ['signup.aws', 'verify.signin.aws', '<EMAIL>']
        fixed_identifiers = [
            'Amazon Web Services, Inc. es una filial de Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc.',
            'This message was created and distributed by Amazon.com, Inc.',
            'Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc. This message was created and distributed by Amazon Web Services, Inc.'
        ]
        patterns = [
            r'[\s>](\d{6})[\s<]',
            r'<strong>(\d{6})</strong>',
            r'<b>(\d{6})</b>',
            r'<span[^>]*>(\d{6})</span>',
            r'(?:código|code|verification|验证码|verificación).*?(\d{6})',
            r'(\d{6}).*?(?:código|code|verification|验证码|verificación)',
        ]
        mail = None
        try:
            if self.stop_flag:
                return None
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=10)
            auth_string = self.generate_auth_string(email_address, access_token)
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            if self.stop_flag:
                return None
            mail.select("INBOX")
            yesterday = time.strftime("%d-%b-%Y", time.gmtime(time.time() - 24*60*60))
            status, messages = mail.search(None, f'SINCE "{yesterday}"')
            if messages[0]:
                email_ids = messages[0].split()
                latest_emails = email_ids[-3:] if len(email_ids) > 3 else email_ids
                for email_id in reversed(latest_emails):
                    if self.stop_flag:
                        break
                    status, msg_data = mail.fetch(email_id, '(RFC822)')
                    raw_email = msg_data[0][1]
                    msg = email.message_from_bytes(raw_email)
                    subject = msg.get("Subject", "")
                    sender = msg.get("From", "")
                    sender_lower = sender.lower()
                    is_aws_sender = any(aws_sender in sender_lower for aws_sender in aws_senders)
                    if is_aws_sender:
                        body_text = self.extract_email_body(msg)
                        has_fixed_identifier = any(identifier in body_text for identifier in fixed_identifiers)
                        if has_fixed_identifier:
                            if "指定的电子邮件地址已与某个 AWS 账户相关联" in body_text:
                                return "已被注册"
                            else:
                                for pattern in patterns:
                                    match = re.search(pattern, body_text, re.IGNORECASE)
                                    if match:
                                        code = match.group(1)
                                        if (len(code) == 6 and code.isdigit() and code != "000000" and code != "010001" and len(set(code)) > 1):
                                            return code
            return None
        except Exception as e:
            write_log(f"获取验证码时出错: {str(e)}", "ERROR")
            return None
        finally:
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass

    def extract_email_body(self, msg):
        """提取邮件正文"""
        body_text = ""
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() in ["text/plain", "text/html"]:
                        body = part.get_payload(decode=True)
                        if body:
                            try:
                                body_text += body.decode('utf-8') + " "
                            except:
                                try:
                                    body_text += body.decode('gbk') + " "
                                except:
                                    body_text += body.decode('utf-8', errors='ignore') + " "
            else:
                body = msg.get_payload(decode=True)
                if body:
                    try:
                        body_text = body.decode('utf-8')
                    except:
                        try:
                            body_text = body.decode('gbk')
                        except:
                            body_text = body.decode('utf-8', errors='ignore')
        except Exception as e:
            write_log(f"提取邮件正文出错: {str(e)}", "ERROR")
        return body_text

    def extract_verification_code(self, body_text):
        """从邮件正文中提取验证码"""
        patterns = [
            r'验证码.*?(\d{6})',
            r'(\d{6}).*?验证码',
            r'code.*?(\d{6})',
            r'(\d{6}).*?code',
            r'verification.*?(\d{6})',
            r'(\d{6}).*?verification',
            r'[\s>](\d{6})[\s<]'
        ]

        for pattern in patterns:
            match = re.search(pattern, body_text, re.IGNORECASE)
            if match:
                code = match.group(1)
                if (len(code) == 6 and code.isdigit() and
                    code != "000000" and code != "010001" and
                    len(set(code)) > 1):
                    return code
        return None

# 重新登录任务
class LoginTask(BaseTask):
    """重新登录任务"""

    def __init__(self, account, callback, task_id=None):
        super().__init__(task_id)
        self.email = account[0]
        self.password = account[1]
        self.refresh_token = account[2]
        self.client_id = account[3]
        self.callback = callback

    def run(self):
        """执行登录操作"""
        try:
            write_log(f"开始重新登录: {self.email}", "INFO")

            if self.stop_flag:
                self.callback(False, "已停止", self.task_id)
                return

            # 获取访问令牌
            access_token = self.get_access_token(self.client_id, self.refresh_token)
            if not access_token:
                self.callback(False, "获取访问令牌失败", self.task_id)
                return

            if self.stop_flag:
                self.callback(False, "已停止", self.task_id)
                return

            # 验证登录
            if self.verify_login(self.email, access_token):
                self.callback(True, "登录成功", self.task_id)
            else:
                self.callback(False, "邮箱登录验证失败", self.task_id)

        except Exception as e:
            write_log(f"重新登录任务出错: {str(e)}", "ERROR")
            self.callback(False, str(e), self.task_id)

    def get_access_token(self, client_id, refresh_token):
        """获取访问令牌"""
        try:
            data = {
                'client_id': client_id,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            response = requests.post('https://login.live.com/oauth20_token.srf', data=data, timeout=10)

            if response.status_code == 200:
                return response.json()['access_token']
            return None
        except Exception as e:
            write_log(f"获取访问令牌失败: {str(e)}", "ERROR")
            return None

    def verify_login(self, email_address, access_token):
        """验证登录"""
        try:
            auth_string = f"user={email_address}\1auth=Bearer {access_token}\1\1"
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=10)
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            mail.select("INBOX")
            mail.close()
            mail.logout()
            return True
        except Exception as e:
            write_log(f"验证登录失败: {str(e)}", "ERROR")
            return False

# 保留原有的SingleLoginWorker类以兼容现有代码
class SingleLoginWorker(QObject):
    """处理单个账户重新登录的工作线程"""
    result_ready = pyqtSignal(bool, str)  # 成功/失败, 消息
    finished = pyqtSignal()
    
    def __init__(self, account):
        super().__init__()
        self.email = account[0]
        self.password = account[1]
        self.refresh_token = account[2]
        self.client_id = account[3]
        self.stop_flag = False
        self.timeout_seconds = 15  # 超时秒数
        self.last_progress_time = time.time()
        
    def run(self):
        """执行登录操作"""
        try:
            write_log(f"开始重新登录账户: {self.email}", "INFO")
            
            # 获取访问令牌
            access_token = self.get_access_token(self.client_id, self.refresh_token)
            if not access_token:
                self.result_ready.emit(False, "获取访问令牌失败")
                self.finished.emit()
                return
                
            # 验证可以登录邮箱
            if not self.verify_login(self.email, access_token):
                self.result_ready.emit(False, "邮箱登录验证失败")
                self.finished.emit()
                return
                
            # 登录成功
            self.result_ready.emit(True, "登录成功")
            write_log(f"账户 {self.email} 重新登录成功", "SUCCESS")
        except Exception as e:
            write_log(f"账户 {self.email} 重新登录出错: {str(e)}", "ERROR")
            self.result_ready.emit(False, str(e))
        finally:
            self.finished.emit()
    
    def stop(self):
        """停止工作线程"""
        self.stop_flag = True
        
    def get_access_token(self, client_id, refresh_token):
        """获取Microsoft OAuth2访问令牌，增加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if self.stop_flag:
                    return None

                data = {
                    'client_id': client_id,
                    'grant_type': 'refresh_token',
                    'refresh_token': refresh_token
                }

                # 设置10秒超时，给网络更多时间
                timeout = 10 if attempt == 0 else 15  # 重试时增加超时时间
                print(f"尝试获取访问令牌 (第{attempt + 1}次)...")
                response = requests.post('https://login.live.com/oauth20_token.srf', data=data, timeout=timeout)

                # 更新进度时间
                self.last_progress_time = time.time()

                if response.status_code == 200:
                    try:
                        token_data = response.json()
                        if 'access_token' in token_data:
                            token = token_data['access_token']
                            print(f"成功获取访问令牌: {token[:10]}...{token[-10:]}")
                            return token
                        else:
                            print(f"响应中没有access_token字段: {response.text}")
                    except Exception as e:
                        print(f"解析访问令牌失败: {str(e)}")
                        print(f"响应内容: {response.text}")
                else:
                    print(f"获取令牌失败，状态码: {response.status_code}, 响应: {response.text}")

                # 如果不是最后一次尝试，等待一下再重试
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)

            except Exception as e:
                print(f"获取访问令牌时出错 (第{attempt + 1}次): {str(e)}")
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)

        print(f"经过{max_retries}次尝试后仍无法获取访问令牌")
        return None
        
    def verify_login(self, email_address, access_token):
        """验证是否可以登录到邮箱"""
        try:
            # 生成认证字符串
            auth_string = self.generate_auth_string(email_address, access_token)
            print(f"尝试使用OAuth2认证登录: {email_address}")
            
            # 使用IMAP协议验证登录，设置10秒超时避免长时间等待
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=10)
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            self.last_progress_time = time.time()  # 更新进度时间
            
            mail.select("INBOX")
            mail.close()
            mail.logout()
            
            print(f"邮箱 {email_address} 登录成功")
            return True
        except Exception as e:
            print(f"邮箱 {email_address} 登录失败: {str(e)}")
            return False
            
    def generate_auth_string(self, user, token):
        """生成OAuth2认证字符串"""
        auth_string = f"user={user}\1auth=Bearer {token}\1\1"
        return auth_string


class CodeWorker(QObject):
    """单独处理验证码获取的工作线程"""
    result_ready = pyqtSignal(str, str)
    finished = pyqtSignal()
    
    def __init__(self, email, password, refresh_token, client_id, main_window=None, row=None):
        super().__init__()
        self.email = email
        self.password = password
        self.refresh_token = refresh_token
        self.client_id = client_id
        self.main_window = main_window  # 添加主窗口引用
        self.row = row  # 添加行号引用
        self.stop_flag = False  # 添加停止标志
        self.last_progress_time = time.time()  # 上次进度更新时间
        self.timeout_seconds = 15  # 缩短超时时间到15秒，提高响应速度

        # 记录初始化信息
        print(f"初始化验证码获取器: {email} (行{row})")
        write_log(f"初始化验证码获取器: {email} (行{row})", "DEBUG")
    
    def stop(self):
        """停止工作线程的执行"""
        self.stop_flag = True
        print(f"停止验证码获取线程: {self.email}")


    
    def run(self):
        try:
            print(f"开始为 {self.email} 获取验证码...")

            # 设置超时检查线程
            self._setup_timeout_check()

            # 检查停止标志
            if self.stop_flag:
                self.result_ready.emit("已停止", "")
                self.finished.emit()
                return

            # 获取访问令牌
            self.last_progress_time = time.time()  # 记录开始时间
            print(f"正在获取访问令牌...")
            write_log(f"正在为 {self.email} 获取访问令牌", "DEBUG")
            access_token = self.get_access_token(self.client_id, self.refresh_token)
            if not access_token or self.stop_flag:
                if self.stop_flag:
                    print(f"线程被停止，退出")
                    write_log(f"线程被停止，退出", "DEBUG")
                    self.result_ready.emit("已停止", "")
                else:
                    print(f"访问令牌获取失败")
                    write_log(f"访问令牌获取失败: 无法刷新访问令牌", "ERROR")
                    self.result_ready.emit("令牌获取失败: 无法刷新访问令牌", "")
                self.finished.emit()
                return

            # 再次检查停止标志
            if self.stop_flag:
                self.result_ready.emit("已停止", "")
                self.finished.emit()
                return

            # 更新进度时间
            self.last_progress_time = time.time()

            # 跳过重复的登录验证，因为EmailWorker已经验证过了
            # 直接使用访问令牌获取验证码，避免重复IMAP连接导致的失败
            print(f"跳过重复登录验证，直接获取验证码: {self.email}")

            # 更新进度时间
            self.last_progress_time = time.time()

            # 尝试获取验证码
            print(f"开始获取验证码...")
            write_log(f"开始为 {self.email} 获取验证码", "DEBUG")
            verification_code = self.get_aws_verification_code(self.email, access_token)
            print(f"验证码获取结果: {verification_code}")
            write_log(f"验证码获取结果: {verification_code}", "DEBUG")

            if self.stop_flag:
                print(f"线程被停止")
                write_log(f"线程被停止", "DEBUG")
                self.result_ready.emit("已停止", "")
            elif verification_code:
                # 获取成功，立即停止超时检查
                print(f"验证码获取成功: {verification_code}")
                write_log(f"验证码获取成功: {verification_code}", "SUCCESS")
                self.stop_flag = True

                # 立即检查并写入AWS监控文件
                self.handle_immediate_aws_response(verification_code)

                # 发送结果信号
                self.result_ready.emit("验证码获取成功", verification_code)
                
                # 立即结束线程
                self.finished.emit()
                return
            else:
                print(f"未找到验证码")
                write_log(f"未找到验证码", "WARNING")

                # 检查是否有AWS请求需要响应失败
                self.handle_immediate_aws_response(None)

                self.result_ready.emit("未找到验证码", "")
        except Exception as e:
            error_msg = str(e)
            write_log(f"验证码获取异常: {error_msg}", "ERROR")

            if self.stop_flag:
                self.result_ready.emit("已停止", "")
            else:
                # 不要将异常信息直接作为status传递，而是使用标准化的错误状态
                if "timeout" in error_msg.lower():
                    self.result_ready.emit("连接超时", "")
                elif "authentication" in error_msg.lower() or "login" in error_msg.lower():
                    self.result_ready.emit("认证失败", "")
                elif "connection" in error_msg.lower():
                    self.result_ready.emit("连接失败", "")
                else:
                    self.result_ready.emit("获取失败", "")

                # 详细错误信息记录到日志，但不传递给UI
                write_log(f"详细错误信息: {error_msg}", "ERROR")

        self.finished.emit()

    def handle_immediate_aws_response(self, verification_code):
        """立即处理AWS响应，确保验证码获取成功后立即写入监控文件"""
        try:
            if not self.main_window or self.row is None:
                write_log(f"无法处理AWS响应：缺少主窗口引用或行号", "DEBUG")
                return

            # 检查是否有对应的AWS请求
            if not hasattr(self.main_window, 'aws_requests') or self.row not in self.main_window.aws_requests:
                write_log(f"行{self.row}无对应的AWS请求，跳过立即响应", "DEBUG")
                return

            # 获取AWS请求信息
            aws_request = self.main_window.aws_requests[self.row].copy()
            thread_id = aws_request.get('thread_id', 0)
            is_multithread = aws_request.get('is_multithread', False)
            timestamp = aws_request.get('timestamp', '')

            write_log(f"立即处理AWS响应: 行{self.row}, 验证码={verification_code}, 线程{thread_id}", "INFO")

            if verification_code and len(verification_code) == 6 and verification_code.isdigit():
                # 验证码有效，立即写入成功响应
                self.main_window.write_response("success", verification_code, timestamp, thread_id, is_multithread)
                write_log(f"立即写入AWS成功响应: {verification_code}", "SUCCESS")

                # 清除AWS请求状态
                self.main_window.aws_requests.pop(self.row, None)
                write_log(f"已清除行{self.row}的AWS请求状态", "DEBUG")
            else:
                # 验证码无效或未获取到，写入失败响应
                error_msg = "未找到有效验证码" if not verification_code else f"验证码格式无效: {verification_code}"
                self.main_window.write_response("error", error_msg, timestamp, thread_id, is_multithread)
                write_log(f"立即写入AWS失败响应: {error_msg}", "ERROR")

                # 清除AWS请求状态
                self.main_window.aws_requests.pop(self.row, None)
                write_log(f"已清除行{self.row}的AWS请求状态", "DEBUG")

        except Exception as e:
            write_log(f"立即处理AWS响应时出错: {str(e)}", "ERROR")

    def _setup_timeout_check(self):
        """设置超时检查线程"""
        def check_timeout():
            start_time = time.time()
            while not self.stop_flag:
                current_time = time.time()
                elapsed = current_time - self.last_progress_time
                total_elapsed = current_time - start_time

                # 检查是否超时
                if elapsed > self.timeout_seconds:
                    print(f"账户 {self.email} 获取验证码超时 ({self.timeout_seconds}秒无响应)")
                    write_log(f"账户 {self.email} 获取验证码超时 ({self.timeout_seconds}秒无响应)", "WARNING")
                    self.stop_flag = True
                    self.result_ready.emit("获取超时", "")
                    break

                # 防止超时检查线程无限运行，最多运行15秒
                if total_elapsed > 15:
                    print(f"超时检查线程运行超过15秒，自动退出")
                    write_log(f"超时检查线程运行超过15秒，自动退出", "DEBUG")
                    break

                time.sleep(0.5)  # 每0.5秒检查一次

        # 创建并启动超时检查线程
        timeout_thread = threading.Thread(target=check_timeout)
        timeout_thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
        timeout_thread.start()
    
    def get_access_token(self, client_id, refresh_token):
        """获取Microsoft OAuth2访问令牌，增加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if self.stop_flag:
                    return None

                data = {
                    'client_id': client_id,
                    'grant_type': 'refresh_token',
                    'refresh_token': refresh_token
                }

                # 设置10秒超时，给网络更多时间
                timeout = 10 if attempt == 0 else 15  # 重试时增加超时时间
                print(f"尝试获取访问令牌 (第{attempt + 1}次)...")
                response = requests.post('https://login.live.com/oauth20_token.srf', data=data, timeout=timeout)

                # 更新进度时间
                self.last_progress_time = time.time()

                if response.status_code == 200:
                    try:
                        token_data = response.json()
                        if 'access_token' in token_data:
                            token = token_data['access_token']
                            print(f"成功获取访问令牌: {token[:10]}...{token[-10:]}")
                            return token
                        else:
                            print(f"响应中没有access_token字段: {response.text}")
                    except Exception as e:
                        print(f"解析访问令牌失败: {str(e)}")
                        print(f"响应内容: {response.text}")
                else:
                    print(f"获取令牌失败，状态码: {response.status_code}, 响应: {response.text}")

                # 如果不是最后一次尝试，等待一下再重试
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)

            except Exception as e:
                print(f"获取访问令牌时出错 (第{attempt + 1}次): {str(e)}")
                if attempt < max_retries - 1:
                    print(f"等待2秒后重试...")
                    time.sleep(2)

        print(f"经过{max_retries}次尝试后仍无法获取访问令牌")
        return None
    
    def verify_login(self, email_address, access_token):
        """验证是否可以登录到邮箱，参考test.py实现"""
        try:
            # 生成认证字符串
            auth_string = self.generate_auth_string(email_address, access_token)
            print(f"尝试使用OAuth2认证登录: {email_address}")
            
            # 使用IMAP协议验证登录，设置10秒超时避免长时间等待
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=10)
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            self.last_progress_time = time.time()  # 更新进度时间
            
            mail.select("INBOX")
            mail.close()
            mail.logout()
            
            print(f"邮箱 {email_address} 登录成功")
            return True
        except Exception as e:
            print(f"邮箱 {email_address} 登录失败: {str(e)}")
            return False
    
    def generate_auth_string(self, user, token):
        """生成OAuth2认证字符串，直接使用test.py的实现"""
        auth_string = f"user={user}\1auth=Bearer {token}\1\1"
        return auth_string
    
    def decode_str(self, s):
        """解码邮件主题，直接使用test.py的实现"""
        if s:
            value, charset = decode_header(s)[0]
            if charset:
                if isinstance(value, bytes):
                    return value.decode(charset)
            return value
        return ""
    
    def get_aws_verification_code(self, email_address, access_token, wait_time=0):
        """获取AWS验证码，同时搜索收件箱和垃圾邮件箱，获取最新的验证码"""
        # 方案1：发件人 + 固定英文标识符识别
        aws_senders = ['signup.aws', 'verify.signin.aws', '<EMAIL>']
        fixed_identifiers = [
            'Amazon Web Services, Inc. es una filial de Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc.',
            'This message was created and distributed by Amazon.com, Inc.',
            'Amazon Web Services, Inc. is a subsidiary of Amazon.com, Inc.',
            'Amazon.com is a registered trademark of Amazon.com, Inc. This message was created and distributed by Amazon Web Services, Inc.'
            '验证您的身份'
        ]
        patterns = [
            r'[\s>](\d{6})[\s<]',
            r'<strong>(\d{6})</strong>',
            r'<b>(\d{6})</b>',
            r'<span[^>]*>(\d{6})</span>',
            r'(?:código|code|verification|验证码|verificación).*?(\d{6})',
            r'(\d{6}).*?(?:código|code|verification|验证码|verificación)',
        ]
        mail = None
        try:
            print(f"正在连接IMAP服务器查找验证码邮件...")

            # 连接到IMAP服务器，设置15秒超时，给更多时间
            mail = imaplib.IMAP4_SSL('outlook.office365.com', timeout=15)
            auth_string = self.generate_auth_string(email_address, access_token)

            print(f"正在进行IMAP OAuth2认证...")
            mail.authenticate('XOAUTH2', lambda x: auth_string)
            print(f"IMAP认证成功")

            # 需要搜索的文件夹列表：收件箱和垃圾邮件箱
            folders_to_search = ["INBOX", "Junk"]  # Outlook的垃圾邮件箱通常叫"Junk"

            # 存储所有找到的AWS验证码邮件，按时间排序
            all_aws_emails = []

            # 遍历每个文件夹
            for folder in folders_to_search:
                if self.stop_flag:
                    break

                try:
                    print(f"正在搜索文件夹: {folder}")
                    mail.select(folder)

                    # 搜索24小时内的邮件
                    yesterday = time.strftime("%d-%b-%Y", time.gmtime(time.time() - 24*60*60))
                    status, messages = mail.search(None, f'SINCE "{yesterday}"')
                    if messages[0]:
                        email_ids = messages[0].split()
                        # 只取最新的3封邮件
                        latest_emails = email_ids[-3:] if len(email_ids) > 4 else email_ids
                        print(f"在{folder}中找到 {len(email_ids)} 封24小时内的邮件，检查最新的 {len(latest_emails)} 封")

                        # 检查每封邮件
                        for email_id in reversed(latest_emails):  # 从最新的开始检查
                            if self.stop_flag:
                                break

                            try:
                                status, msg_data = mail.fetch(email_id, '(RFC822)')
                                self.last_progress_time = time.time()

                                # 解析邮件内容
                                raw_email = msg_data[0][1]
                                msg = email.message_from_bytes(raw_email)

                                subject = self.decode_str(msg["Subject"])
                                sender = self.decode_str(msg.get("From", ""))
                                date_str = msg.get("Date", "")

                                print(f"检查邮件: {subject} (来自: {sender})")

                                # 方案1：发件人 + 固定英文标识符识别
                                sender_lower = sender.lower()
                                is_aws_sender = any(aws_sender in sender_lower for aws_sender in aws_senders)
                                if is_aws_sender:
                                    print(f"  发现AWS发件人: {sender}")
                                    # 提取邮件正文用于进一步检查
                                    email_body_text = ""
                                    if msg.is_multipart():
                                        for part in msg.walk():
                                            content_type = part.get_content_type()
                                            if content_type == "text/plain" or content_type == "text/html":
                                                try:
                                                    body = part.get_payload(decode=True)
                                                    if body:
                                                        try:
                                                            body_text = body.decode('utf-8')
                                                        except:
                                                            try:
                                                                body_text = body.decode('gbk')
                                                            except:
                                                                body_text = body.decode('utf-8', errors='ignore')
                                                        email_body_text += body_text + " "
                                                except Exception as e:
                                                    print(f"解析邮件部分时出错: {str(e)}")
                                                    continue
                                    else:
                                        try:
                                            body = msg.get_payload(decode=True)
                                            if body:
                                                try:
                                                    email_body_text = body.decode('utf-8')
                                                except:
                                                    try:
                                                        email_body_text = body.decode('gbk')
                                                    except:
                                                        email_body_text = body.decode('utf-8', errors='ignore')
                                        except Exception as e:
                                            print(f"解析邮件内容时出错: {str(e)}")
                                    has_fixed_identifier = any(identifier in email_body_text for identifier in fixed_identifiers)
                                    if has_fixed_identifier:
                                        print(f"  匹配AWS固定标识符")
                                        if "指定的电子邮件地址已与某个 AWS 账户相关联" in email_body_text:
                                            verification_code = "已被注册"
                                            email_type = "already_registered"
                                            print(f"  确认为已注册邮件")
                                        else:
                                            email_type = "verification_code"
                                            verification_code = None
                                            print(f"  确认为验证码邮件")
                                    else:
                                        print(f"  未找到AWS固定标识符，跳过")
                                        continue
                                else:
                                    print(f"  非AWS发件人，跳过")
                                    continue

                                print(f"发现目标邮件: {subject}")

                                # 根据邮件类型处理
                                if email_type == "already_registered":
                                    verification_code = "已被注册"
                                    print(f"  确认为已注册邮件")
                                elif email_type == "verification_code":
                                    # 查找验证码
                                    verification_code = None
                                    for pattern in patterns:
                                        code_match = re.search(pattern, email_body_text, re.IGNORECASE)
                                        if code_match:
                                            verification_code = code_match.group(1)
                                            print(f"  找到验证码: {verification_code}")
                                            break

                                # 处理找到的结果
                                if verification_code:
                                    # 解析邮件时间
                                    try:
                                        email_time = email.utils.parsedate_to_datetime(date_str)
                                        timestamp = email_time.timestamp()
                                    except:
                                        timestamp = time.time()  # 如果解析失败，使用当前时间

                                    if email_type == "already_registered":
                                        # 已注册邮件，直接记录
                                        all_aws_emails.append({
                                            'type': 'already_registered',
                                            'code': verification_code,
                                            'timestamp': timestamp,
                                            'subject': subject,
                                            'folder': folder,
                                            'sender': sender
                                        })
                                        print(f"在{folder}中找到已注册邮件: {subject}")
                                    elif email_type == "verification_code":
                                        # 验证码邮件，需要验证验证码有效性
                                        if (len(verification_code) == 6 and
                                            verification_code.isdigit() and
                                            verification_code != "000000" and
                                            verification_code != "010001" and
                                            len(set(verification_code)) > 1):  # 不能是重复数字

                                            all_aws_emails.append({
                                                'type': 'verification_code',
                                                'code': verification_code,
                                                'timestamp': timestamp,
                                                'subject': subject,
                                                'folder': folder,
                                                'sender': sender
                                            })
                                            print(f"在{folder}中找到有效AWS验证码: {verification_code} (邮件: {subject})")
                                        else:
                                            print(f"  跳过无效验证码: {verification_code}")

                            except Exception as e:
                                print(f"处理邮件时出错: {str(e)}")
                                continue
                    else:
                        print(f"在{folder}中未找到24小时内的邮件")

                except Exception as e:
                    print(f"搜索文件夹 {folder} 时出错: {str(e)}")
                    # 如果是垃圾邮件箱不存在，尝试其他可能的名称
                    if folder == "Junk":
                        alternative_names = ["Spam", "Junk Email", "[Gmail]/Spam"]
                        for alt_name in alternative_names:
                            try:
                                print(f"尝试备用垃圾邮件箱名称: {alt_name}")
                                mail.select(alt_name)
                                print(f"成功访问垃圾邮件箱: {alt_name}")
                                # 这里可以重复上面的搜索逻辑，但为了简化，我们跳过
                                break
                            except:
                                continue
                    continue

            # 关闭IMAP连接
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass

            # 处理找到的邮件
            if all_aws_emails:
                # 按时间戳排序，最新的在最后
                all_aws_emails.sort(key=lambda x: x['timestamp'])
                latest_email = all_aws_emails[-1]

                print(f"找到 {len(all_aws_emails)} 个目标邮件，最新的:")
                print(f"类型: {latest_email.get('type', 'unknown')}")
                print(f"结果: {latest_email['code']}")
                print(f"来源: {latest_email['folder']} - {latest_email['subject']}")
                print(f"发件人: {latest_email['sender']}")

                # 按优先级返回结果
                if latest_email.get('type') == 'already_registered':
                    return "已被注册"
                elif latest_email.get('type') == 'verification_code':
                    return latest_email['code']
                else:
                    # 如果最新的不是目标邮件，但有验证码邮件，返回未获取验证码
                    print("最新邮件不是目标类型")
                    return None
            else:
                print("在收件箱和垃圾邮件箱的24小时内邮件中都未找到目标邮件")
                return None

        except Exception as e:
            print(f"获取验证码时出错: {str(e)}")
            # 确保IMAP连接被正确关闭
            if mail:
                try:
                    mail.close()
                    mail.logout()
                except:
                    pass
            return None
            
def check_dependencies():
    """检查必要的依赖库是否已安装"""
    missing_libs = []
    
    # 检查核心依赖
    try:
        import PyQt5
    except ImportError:
        missing_libs.append("PyQt5")
    
    try:
        import requests
    except ImportError:
        missing_libs.append("requests")

    try:
        from watchdog.observers import Observer
        from watchdog.events import FileSystemEventHandler
    except ImportError:
        missing_libs.append("watchdog")
    
    # 如果有缺失的库，给出提示
    if missing_libs:
        error_message = f"缺少必要的依赖库: {', '.join(missing_libs)}\n"
        error_message += "请使用以下命令安装缺失的库:\n"
        error_message += "pip install " + " ".join(missing_libs)
        print(error_message)
        
        # 尝试显示图形界面错误
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            app = QApplication(sys.argv)
            QMessageBox.critical(None, '依赖错误', error_message)
            app.quit()
        except:
            pass
            
        return False
    return True

def main():
    # 设置Qt插件路径
    qt_plugin_path = os.path.join(os.path.dirname(sys.executable), 'Lib', 'site-packages', 'PyQt5', 'Qt5', 'plugins')
    if os.path.exists(qt_plugin_path):
        os.environ['QT_PLUGIN_PATH'] = qt_plugin_path
        write_log(f"设置Qt插件路径: {qt_plugin_path}", "INFO")
    
    write_log("=== AWS验证码获取工具启动 ===", "INFO")
    write_log(f"Python版本: {sys.version}", "INFO")
    write_log(f"系统平台: {sys.platform}", "INFO")
    
    # 设置Windows任务栏图标
    set_windows_taskbar_icon()
    
    app = QApplication(sys.argv)
    
    # 设置应用图标
    icon_path = get_icon_path()
    if icon_path:
        app_icon = QIcon(icon_path)
        app.setWindowIcon(app_icon)
        write_log(f"已设置应用图标: {icon_path}", "INFO")
    
    ex = AWSVerificationTool()
    ex.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    # 检查依赖
    check_dependencies()
    # 启动应用
    main() 
